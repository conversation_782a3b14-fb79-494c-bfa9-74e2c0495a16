import os
import shutil
import sys
import json
import logging
import time
import zipfile
import threading
from datetime import datetime
from tkinter import *
from tkinter import ttk, messagebox, filedialog

# 配置日志记录
log_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'enc_auto_xfer.log')
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8', mode='a'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 定义常量
FOLDER_MONITOR_INTERVAL = 5000  # 文件夹监控间隔(ms)
FOLDER_WRITE_CHECK_DELAY = 5000  # 文件夹写入完成检测延时(ms)

class ENCAutoXferApp:
    def __init__(self, root):
        self.root = root
        self.root.title("ENC WQAR数据自动传输程序")
        self.root.geometry("750x650")
        self.root.configure(bg='#f5f5f5')

        # 设置窗口图标
        try:
            icon_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app.ico')
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
        except:
            pass

        # 初始化路径属性
        self.path1 = r'Z:\DATA_BAK\ENC_BAK'  # 源文件夹
        self.path2 = r'E:\ENC'  # 目标文件夹
        self.cast_source = os.path.join(self.path2, 'ENC_CAST')
        self.comac_source = os.path.join(self.path2, 'ENC_COMAC')
        self.cast_target = r'D:\NON-WGL\CAST'
        self.comac_target = r'D:\NON-WGL\COMAC'

        # 初始化配置文件路径
        self.config_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "ENC.json")
        self.ac_type_file = r'Z:\DATA_BAK\config\AC_TYPE.json'

        # 初始化统计属性
        self.enc_file_count = 0
        self.cast_copied_files = 0
        self.comac_copied_files = 0
        self.total_runtime_seconds = 0
        self.cast_total_time = 0
        self.comac_total_time = 0

        # 初始化状态属性
        self.is_running = False
        self.is_cast_copying = False
        self.is_comac_copying = False
        self.start_time = None

        # 初始化选择框状态
        self.cast_enabled = BooleanVar(value=True)
        self.comac_enabled = BooleanVar(value=True)

        # 初始化监控变量
        self.processed_folders = set()
        self.pending_folders = []
        self.processing_folders = []
        self.last_folder_sizes = {}
        self.cast_pending_files = []
        self.comac_pending_files = []
        self.cast_waiting_seconds = 0
        self.comac_waiting_seconds = 0

        # 加载配置
        self.load_config()
        self.load_ac_type_config()

        # 创建UI
        self.create_ui()

        # 启动监控线程
        self.monitor_thread = None
        self.stop_monitoring = False

    def load_ac_type_config(self):
        """加载飞机型号配置文件"""
        try:
            # 首先尝试加载主配置文件
            if os.path.exists(self.ac_type_file):
                # 尝试多种编码方式
                encodings = ['utf-8-sig', 'utf-8', 'gbk', 'latin1']
                for encoding in encodings:
                    try:
                        with open(self.ac_type_file, 'r', encoding=encoding) as f:
                            self.ac_type_map = json.load(f)
                        logger.info(f"成功加载主配置文件（编码：{encoding}），共{len(self.ac_type_map)}架飞机")
                        return
                    except (UnicodeDecodeError, json.JSONDecodeError):
                        continue
                raise Exception("所有编码方式都失败")
            else:
                # 如果主配置文件不存在，尝试使用测试配置文件
                test_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "test_AC_TYPE.json")
                if os.path.exists(test_file):
                    with open(test_file, 'r', encoding='utf-8') as f:
                        self.ac_type_map = json.load(f)
                    logger.info(f"使用测试配置文件，共{len(self.ac_type_map)}架飞机")
                else:
                    self.ac_type_map = {}
                    logger.warning(f"配置文件不存在: {self.ac_type_file} 和 {test_file}")
        except Exception as e:
            self.ac_type_map = {}
            logger.error(f"加载飞机型号配置失败: {str(e)}")

    def is_arj21_aircraft(self, aircraft_code):
        """判断是否为ARJ21机型"""
        return self.ac_type_map.get(aircraft_code, '') == 'ARJ21'

    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.enc_file_count = config.get('enc_file_count', 0)
                    self.cast_copied_files = config.get('cast_copied_files', 0)
                    self.comac_copied_files = config.get('comac_copied_files', 0)
                    self.total_runtime_seconds = config.get('total_runtime_seconds', 0)
                    self.cast_total_time = config.get('cast_total_time', 0)
                    self.comac_total_time = config.get('comac_total_time', 0)
                    self.cast_enabled.set(config.get('cast_enabled', True))
                    self.comac_enabled.set(config.get('comac_enabled', True))
        except Exception as e:
            logger.error(f"加载配置文件出错: {str(e)}")

    def save_config(self):
        """保存配置文件"""
        try:
            config = {
                'enc_file_count': self.enc_file_count,
                'cast_copied_files': self.cast_copied_files,
                'comac_copied_files': self.comac_copied_files,
                'total_runtime_seconds': self.total_runtime_seconds,
                'cast_total_time': self.cast_total_time,
                'comac_total_time': self.comac_total_time,
                'cast_enabled': self.cast_enabled.get(),
                'comac_enabled': self.comac_enabled.get(),
                'last_run_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
        except Exception as e:
            logger.error(f"保存配置文件出错: {str(e)}")

    def create_ui(self):
        """创建用户界面，参考Non_WGL_AUTO.py的风格"""
        # 设置样式
        style = ttk.Style()
        style.theme_use('clam')

        # 配置样式
        style.configure('Title.TLabel', font=('Microsoft YaHei UI', 12, 'bold'), foreground='#2c3e50')
        style.configure('Status.TLabel', font=('Microsoft YaHei UI', 10, 'bold'), foreground='#34495e')
        style.configure('Info.TLabel', font=('Microsoft YaHei UI', 9), foreground='#7f8c8d')
        style.configure('Time.TLabel', font=('Microsoft YaHei UI', 11, 'bold'), foreground='#e74c3c')

        # 主框架
        main_frame = Frame(self.root, bg='#f5f5f5', padx=15, pady=15)
        main_frame.pack(fill=BOTH, expand=True)

        # 标题
        title_label = Label(main_frame, text="ENC WQAR数据自动传输程序",
                           font=('Microsoft YaHei UI', 16, 'bold'),
                           fg='#2c3e50', bg='#f5f5f5')
        title_label.pack(pady=(0, 20))

        # 路径设置框架
        path_frame = LabelFrame(main_frame, text="路径设置", font=('Microsoft YaHei UI', 11, 'bold'),
                               fg='#2c3e50', bg='white', padx=15, pady=10)
        path_frame.pack(fill=X, pady=(0, 15))

        # 源路径
        source_frame = Frame(path_frame, bg='white')
        source_frame.pack(fill=X, pady=(0, 8))
        Label(source_frame, text="源文件夹:", font=('Microsoft YaHei UI', 10),
              fg='#2c3e50', bg='white', width=12, anchor='e').pack(side=LEFT, padx=(0, 10))
        self.source_var = StringVar(value=self.path1)
        source_entry = Entry(source_frame, textvariable=self.source_var, state='readonly',
                            font=('Microsoft YaHei UI', 9), bg='#ecf0f1', relief=FLAT, bd=5)
        source_entry.pack(side=LEFT, fill=X, expand=True)

        # 目标路径
        target_frame = Frame(path_frame, bg='white')
        target_frame.pack(fill=X)
        Label(target_frame, text="目标文件夹:", font=('Microsoft YaHei UI', 10),
              fg='#2c3e50', bg='white', width=12, anchor='e').pack(side=LEFT, padx=(0, 10))
        self.target_var = StringVar(value=self.path2)
        target_entry = Entry(target_frame, textvariable=self.target_var, state='readonly',
                            font=('Microsoft YaHei UI', 9), bg='#ecf0f1', relief=FLAT, bd=5)
        target_entry.pack(side=LEFT, fill=X, expand=True)

        # 复制选项框架
        option_frame = LabelFrame(main_frame, text="复制选项", font=('Microsoft YaHei UI', 11, 'bold'),
                                 fg='#2c3e50', bg='white', padx=15, pady=10)
        option_frame.pack(fill=X, pady=(0, 15))

        options_inner = Frame(option_frame, bg='white')
        options_inner.pack(fill=X)

        cast_cb = Checkbutton(options_inner, text="CAST数据复制", variable=self.cast_enabled,
                             font=('Microsoft YaHei UI', 10), fg='#2c3e50', bg='white',
                             activebackground='white', selectcolor='#27ae60')
        cast_cb.pack(side=LEFT, padx=(0, 30))

        comac_cb = Checkbutton(options_inner, text="COMAC数据复制", variable=self.comac_enabled,
                              font=('Microsoft YaHei UI', 10), fg='#2c3e50', bg='white',
                              activebackground='white', selectcolor='#27ae60')
        comac_cb.pack(side=LEFT)

        # 状态信息框架
        status_frame = LabelFrame(main_frame, text="复制状态", font=('Microsoft YaHei UI', 11, 'bold'),
                                 fg='#2c3e50', bg='white', padx=15, pady=10)
        status_frame.pack(fill=X, pady=(0, 15))

        # CAST状态
        cast_status_frame = Frame(status_frame, bg='white')
        cast_status_frame.pack(fill=X, pady=(0, 8))
        Label(cast_status_frame, text="CAST:", font=('Microsoft YaHei UI', 10, 'bold'),
              fg='#3498db', bg='white', width=8, anchor='w').pack(side=LEFT)
        self.cast_status_var = StringVar(value="等待开始...")
        cast_status_label = Label(cast_status_frame, textvariable=self.cast_status_var,
                                 font=('Microsoft YaHei UI', 10), fg='#34495e', bg='#ecf0f1',
                                 relief=FLAT, bd=5, anchor='w', padx=10)
        cast_status_label.pack(side=LEFT, fill=X, expand=True)

        # COMAC状态
        comac_status_frame = Frame(status_frame, bg='white')
        comac_status_frame.pack(fill=X, pady=(0, 8))
        Label(comac_status_frame, text="COMAC:", font=('Microsoft YaHei UI', 10, 'bold'),
              fg='#e67e22', bg='white', width=8, anchor='w').pack(side=LEFT)
        self.comac_status_var = StringVar(value="等待开始...")
        comac_status_label = Label(comac_status_frame, textvariable=self.comac_status_var,
                                  font=('Microsoft YaHei UI', 10), fg='#34495e', bg='#ecf0f1',
                                  relief=FLAT, bd=5, anchor='w', padx=10)
        comac_status_label.pack(side=LEFT, fill=X, expand=True)

        # 统计信息
        stats_frame = Frame(status_frame, bg='white')
        stats_frame.pack(fill=X)
        self.stats_var = StringVar(value=f"ENC文件数: {self.enc_file_count} | CAST已复制: {self.cast_copied_files} | COMAC已复制: {self.comac_copied_files}")
        stats_label = Label(stats_frame, textvariable=self.stats_var,
                           font=('Microsoft YaHei UI', 9), fg='#7f8c8d', bg='white')
        stats_label.pack()

        # 等待时间框架
        time_frame = LabelFrame(main_frame, text="等待时间", font=('Microsoft YaHei UI', 11, 'bold'),
                               fg='#2c3e50', bg='white', padx=15, pady=10)
        time_frame.pack(fill=X, pady=(0, 15))

        time_inner = Frame(time_frame, bg='white')
        time_inner.pack(fill=X)

        # 当前等待时间
        current_wait_frame = Frame(time_inner, bg='#fdf2f2', relief=RAISED, bd=2)
        current_wait_frame.pack(side=LEFT, fill=X, expand=True, padx=(0, 10))
        self.current_wait_var = StringVar(value="当前等待: 00:00")
        current_wait_label = Label(current_wait_frame, textvariable=self.current_wait_var,
                                  font=('Microsoft YaHei UI', 12, 'bold'), fg='#e74c3c', bg='#fdf2f2', pady=8)
        current_wait_label.pack()

        # 平均等待时间
        avg_wait_frame = Frame(time_inner, bg='#fdf2f2', relief=RAISED, bd=2)
        avg_wait_frame.pack(side=LEFT, fill=X, expand=True)
        self.avg_wait_var = StringVar(value="平均等待: 00:00")
        avg_wait_label = Label(avg_wait_frame, textvariable=self.avg_wait_var,
                              font=('Microsoft YaHei UI', 12, 'bold'), fg='#e74c3c', bg='#fdf2f2', pady=8)
        avg_wait_label.pack()

        # 控制按钮框架
        control_frame = Frame(main_frame, bg='#f5f5f5')
        control_frame.pack(fill=X, pady=(15, 0))

        button_frame = Frame(control_frame, bg='#f5f5f5')
        button_frame.pack()

        self.start_button = Button(button_frame, text="🚀 开始监控", command=self.start_monitoring,
                                  font=('Microsoft YaHei UI', 12, 'bold'), fg='white', bg='#27ae60',
                                  activebackground='#229954', relief=RAISED, bd=3, padx=20, pady=8)
        self.start_button.pack(side=LEFT, padx=(0, 15))

        self.stop_button = Button(button_frame, text="⏹ 暂停监控", command=self.stop_monitoring_func,
                                 font=('Microsoft YaHei UI', 12, 'bold'), fg='white', bg='#e74c3c',
                                 activebackground='#c0392b', relief=RAISED, bd=3, padx=20, pady=8, state=DISABLED)
        self.stop_button.pack(side=LEFT)

        # 更新统计显示
        self.update_stats_display()
        self.update_avg_wait_display()

    def update_stats_display(self):
        """更新统计信息显示"""
        self.stats_var.set(
            f"ENC文件数: {self.enc_file_count} | CAST已复制: {self.cast_copied_files} | COMAC已复制: {self.comac_copied_files}"
        )

    def update_avg_wait_display(self):
        """更新平均等待时间显示"""
        total_copied = self.cast_copied_files + self.comac_copied_files
        if total_copied > 0:
            total_time = self.cast_total_time + self.comac_total_time
            avg_seconds = total_time / total_copied
            avg_minutes = int(avg_seconds // 60)
            avg_secs = int(avg_seconds % 60)
            self.avg_wait_var.set(f"平均等待: {avg_minutes:02d}:{avg_secs:02d}")
        else:
            self.avg_wait_var.set("平均等待: 00:00")

    def get_folder_size(self, folder_path):
        """计算文件夹大小"""
        total_size = 0
        try:
            for dirpath, dirnames, filenames in os.walk(folder_path):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    if os.path.exists(filepath):
                        total_size += os.path.getsize(filepath)
        except Exception as e:
            logger.error(f"计算文件夹大小出错: {str(e)}")
        return total_size

    def create_zip_file(self, source_folder, zip_path):
        """创建ZIP文件，只包含文件夹内的文件，不包含文件夹本身"""
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(source_folder):
                for file in files:
                    file_path = os.path.join(root, file)
                    # 计算相对路径，不包含源文件夹名
                    arcname = os.path.relpath(file_path, source_folder)
                    zipf.write(file_path, arcname)

    def process_folder(self, folder_name):
        """处理新文件夹：压缩并分类"""
        try:
            # 提取飞机号（前6个字符）
            aircraft_code = folder_name[:6].upper()
            logger.info(f"处理文件夹: {folder_name}, 飞机号: {aircraft_code}")

            # 创建目标目录
            os.makedirs(self.cast_source, exist_ok=True)
            os.makedirs(self.comac_source, exist_ok=True)

            # 压缩文件夹
            zip_filename = f"{folder_name}M.zip"
            source_folder_path = os.path.join(self.path1, folder_name)
            cast_zip_path = os.path.join(self.cast_source, zip_filename)

            self.create_zip_file(source_folder_path, cast_zip_path)
            logger.info(f"创建压缩文件: {cast_zip_path}")

            # 判断是否为ARJ21机型
            if self.is_arj21_aircraft(aircraft_code):
                comac_zip_path = os.path.join(self.comac_source, zip_filename)
                shutil.copy2(cast_zip_path, comac_zip_path)
                logger.info(f"ARJ21机型，复制到COMAC: {comac_zip_path}")

            # 更新统计
            self.enc_file_count += 1
            self.processed_folders.add(folder_name)
            self.root.after(0, self.update_stats_display)
            self.save_config()

            logger.info(f"文件夹 {folder_name} 处理完成")

        except Exception as e:
            logger.error(f"处理文件夹 {folder_name} 出错: {str(e)}")

    def start_monitoring(self):
        """开始监控"""
        # 确保必要的目录存在
        try:
            os.makedirs(self.path1, exist_ok=True)
            os.makedirs(self.path2, exist_ok=True)
            os.makedirs(self.cast_source, exist_ok=True)
            os.makedirs(self.comac_source, exist_ok=True)
            os.makedirs(self.cast_target, exist_ok=True)
            os.makedirs(self.comac_target, exist_ok=True)
        except Exception as e:
            messagebox.showerror("错误", f"无法创建必要目录: {str(e)}")
            return

        self.is_running = True
        self.start_time = datetime.now()
        self.start_button.config(state=DISABLED)
        self.stop_button.config(state=NORMAL)
        self.stop_monitoring = False

        self.cast_status_var.set("监控中...")
        self.comac_status_var.set("监控中...")

        # 启动监控线程
        self.monitor_thread = threading.Thread(target=self.monitoring_loop, daemon=True)
        self.monitor_thread.start()

        # 启动UI更新定时器
        self.update_countdown()

        logger.info("开始监控")

    def stop_monitoring_func(self):
        """停止监控"""
        self.is_running = False
        self.stop_monitoring = True

        self.start_button.config(state=NORMAL)
        self.stop_button.config(state=DISABLED)

        self.cast_status_var.set("已停止")
        self.comac_status_var.set("已停止")

        # 保存配置
        self.save_config()

        logger.info("监控已停止")

    def update_countdown(self):
        """更新等待时间显示"""
        if not self.is_running:
            return

        try:
            # 优先显示CAST的等待时间，如果CAST没有文件则显示COMAC的
            cast_files = len([f for f in os.listdir(self.cast_source) if f.lower().endswith('.zip')]) if os.path.exists(self.cast_source) else 0
            comac_files = len([f for f in os.listdir(self.comac_source) if f.lower().endswith('.zip')]) if os.path.exists(self.comac_source) else 0

            if cast_files > 0 and self.cast_enabled.get():
                self.cast_waiting_seconds += 1
                minutes = self.cast_waiting_seconds // 60
                seconds = self.cast_waiting_seconds % 60
                self.current_wait_var.set(f"当前等待(CAST): {minutes:02d}:{seconds:02d}")
            elif comac_files > 0 and self.comac_enabled.get():
                self.comac_waiting_seconds += 1
                minutes = self.comac_waiting_seconds // 60
                seconds = self.comac_waiting_seconds % 60
                self.current_wait_var.set(f"当前等待(COMAC): {minutes:02d}:{seconds:02d}")
            else:
                self.current_wait_var.set("当前等待: 00:00")

        except Exception as e:
            logger.error(f"更新等待时间显示出错: {str(e)}")

        # 每秒更新一次
        if self.is_running:
            self.root.after(1000, self.update_countdown)

    def monitoring_loop(self):
        """监控循环"""
        while self.is_running and not self.stop_monitoring:
            try:
                # 监控源文件夹
                self.monitor_source_folder()

                # 检查目标文件夹
                if self.cast_enabled.get():
                    self.check_cast_folder_empty()

                if self.comac_enabled.get():
                    self.check_comac_folder_empty()

                time.sleep(5)  # 每5秒检查一次

            except Exception as e:
                logger.error(f"监控循环出错: {str(e)}")
                time.sleep(5)

    def monitor_source_folder(self):
        """监控源文件夹，检测新文件夹"""
        if not self.is_running:
            return

        try:
            # 获取所有文件夹
            if not os.path.exists(self.path1):
                return

            current_folders = [f for f in os.listdir(self.path1)
                             if os.path.isdir(os.path.join(self.path1, f)) and not f.startswith('.')]

            # 检查新文件夹
            for folder in current_folders:
                if folder not in self.processed_folders and folder not in self.processing_folders:
                    self.processing_folders.append(folder)
                    self.check_folder_write_complete(folder)

        except Exception as e:
            logger.error(f"监控源文件夹出错: {str(e)}")

    def check_folder_write_complete(self, folder):
        """检查文件夹是否写入完成"""
        if not self.is_running:
            return

        folder_path = os.path.join(self.path1, folder)
        try:
            if not os.path.exists(folder_path):
                if folder in self.processing_folders:
                    self.processing_folders.remove(folder)
                return

            # 计算文件夹大小
            current_size = self.get_folder_size(folder_path)

            if folder in self.last_folder_sizes:
                # 如果文件夹大小没有变化，认为写入完成
                if current_size == self.last_folder_sizes[folder]:
                    if folder in self.processing_folders:
                        self.processing_folders.remove(folder)
                    self.process_folder(folder)
                    return
                else:
                    self.last_folder_sizes[folder] = current_size
            else:
                self.last_folder_sizes[folder] = current_size

            # 5秒后再次检查
            time.sleep(5)
            if self.is_running:
                self.check_folder_write_complete(folder)

        except Exception as e:
            logger.error(f"检查文件夹写入完成出错: {str(e)}")
            if folder in self.processing_folders:
                self.processing_folders.remove(folder)

    def check_cast_folder_empty(self):
        """检查CAST目标文件夹是否为空"""
        if not self.is_running or not self.cast_enabled.get() or self.is_cast_copying:
            return

        try:
            if not os.path.exists(self.cast_target):
                return

            target_files = [f for f in os.listdir(self.cast_target) if not f.startswith('.')]

            if len(target_files) == 0:  # 文件夹为空
                # 检查是否有待复制文件
                if os.path.exists(self.cast_source):
                    source_files = [f for f in os.listdir(self.cast_source) if f.lower().endswith('.zip')]

                    if source_files and not self.is_cast_copying:
                        self.is_cast_copying = True
                        self.copy_cast_files(source_files[0])

        except Exception as e:
            logger.error(f"检查CAST文件夹出错: {str(e)}")

    def check_comac_folder_empty(self):
        """检查COMAC目标文件夹是否为空"""
        if not self.is_running or not self.comac_enabled.get() or self.is_comac_copying:
            return

        try:
            if not os.path.exists(self.comac_target):
                return

            target_files = [f for f in os.listdir(self.comac_target) if not f.startswith('.')]

            if len(target_files) == 0:  # 文件夹为空
                # 检查是否有待复制文件
                if os.path.exists(self.comac_source):
                    source_files = [f for f in os.listdir(self.comac_source) if f.lower().endswith('.zip')]

                    if source_files and not self.is_comac_copying:
                        self.is_comac_copying = True
                        self.copy_comac_files(source_files[0])

        except Exception as e:
            logger.error(f"检查COMAC文件夹出错: {str(e)}")

    def copy_cast_files(self, file_to_copy):
        """复制CAST文件"""
        try:
            source_path = os.path.join(self.cast_source, file_to_copy)
            target_path = os.path.join(self.cast_target, file_to_copy)

            self.root.after(0, lambda: self.cast_status_var.set(f"正在复制 {file_to_copy}"))

            copy_start_time = time.time()

            # 确保目标目录存在
            os.makedirs(self.cast_target, exist_ok=True)

            # 复制文件
            shutil.copy2(source_path, target_path)

            # 移动源文件到已复制目录
            copied_dir = os.path.join(self.cast_source, "Copied")
            os.makedirs(copied_dir, exist_ok=True)
            shutil.move(source_path, os.path.join(copied_dir, file_to_copy))

            # 更新统计
            copy_duration = time.time() - copy_start_time
            self.cast_total_time += copy_duration
            self.cast_copied_files += 1

            # 重置等待时间
            self.cast_waiting_seconds = 0

            self.root.after(0, lambda: self.cast_status_var.set(f"复制完成 {file_to_copy}"))
            self.root.after(0, self.update_stats_display)
            self.root.after(0, self.update_avg_wait_display)
            self.save_config()

            logger.info(f"CAST文件复制完成: {file_to_copy}")

        except Exception as e:
            self.root.after(0, lambda: self.cast_status_var.set(f"复制失败 {str(e)}"))
            logger.error(f"CAST文件复制失败: {str(e)}")

        finally:
            self.is_cast_copying = False

    def copy_comac_files(self, file_to_copy):
        """复制COMAC文件"""
        try:
            source_path = os.path.join(self.comac_source, file_to_copy)
            target_path = os.path.join(self.comac_target, file_to_copy)

            self.root.after(0, lambda: self.comac_status_var.set(f"正在复制 {file_to_copy}"))

            copy_start_time = time.time()

            # 确保目标目录存在
            os.makedirs(self.comac_target, exist_ok=True)

            # 复制文件
            shutil.copy2(source_path, target_path)

            # 移动源文件到已复制目录
            copied_dir = os.path.join(self.comac_source, "Copied")
            os.makedirs(copied_dir, exist_ok=True)
            shutil.move(source_path, os.path.join(copied_dir, file_to_copy))

            # 更新统计
            copy_duration = time.time() - copy_start_time
            self.comac_total_time += copy_duration
            self.comac_copied_files += 1

            # 重置等待时间
            self.comac_waiting_seconds = 0

            self.root.after(0, lambda: self.comac_status_var.set(f"复制完成 {file_to_copy}"))
            self.root.after(0, self.update_stats_display)
            self.root.after(0, self.update_avg_wait_display)
            self.save_config()

            logger.info(f"COMAC文件复制完成: {file_to_copy}")

        except Exception as e:
            self.root.after(0, lambda: self.comac_status_var.set(f"复制失败 {str(e)}"))
            logger.error(f"COMAC文件复制失败: {str(e)}")

        finally:
            self.is_comac_copying = False

if __name__ == "__main__":
    root = Tk()
    app = ENCAutoXferApp(root)

    # 程序关闭时保存配置
    def on_closing():
        app.stop_monitoring_func()
        app.save_config()
        root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_closing)

    # 居中显示窗口
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f'{width}x{height}+{x}+{y}')

    root.mainloop()