import os
import shutil
import sys
import json
import logging
import time
import zipfile
import threading
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                             QPushButton, QLabel, QLineEdit, QWidget, QMessageBox,
                             QGroupBox, QFrame, QDesktopWidget, QCheckBox, QGridLayout,
                             QProgressBar, QTextEdit, QSplitter)
from PyQt5.QtCore import QTimer, Qt, QThread, pyqtSignal, QPropertyAnimation, QEasingCurve
from PyQt5.QtGui import QFont, QPalette, QColor, QPixmap, QIcon

# 配置日志记录
log_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'enc_auto_xfer.log')
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8', mode='a'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 定义常量
FOLDER_MONITOR_INTERVAL = 5000  # 文件夹监控间隔(ms)
FOLDER_WRITE_CHECK_DELAY = 5000  # 文件夹写入完成检测延时(ms)

class ENCAutoXferApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("ENC WQAR数据自动传输程序")

        # 设置窗口图标
        try:
            icon_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app.ico')
            if os.path.exists(icon_path):
                self.setWindowIcon(QIcon(icon_path))
        except:
            pass

        # 获取屏幕信息和DPI缩放
        self.setup_display_settings()

        # 初始化路径属性
        self.path1 = r'Z:\DATA_BAK\ENC_BAK'  # 源文件夹
        self.path2 = r'E:\ENC'  # 目标文件夹
        self.cast_source = os.path.join(self.path2, 'ENC_CAST')
        self.comac_source = os.path.join(self.path2, 'ENC_COMAC')
        self.cast_target = r'D:\NON-WGL\CAST'
        self.comac_target = r'D:\NON-WGL\COMAC'

        # 初始化配置文件路径
        self.config_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "ENC.json")
        self.ac_type_file = r'Z:\DATA_BAK\config\AC_TYPE.json'

        # 初始化统计属性
        self.enc_file_count = 0
        self.cast_copied_files = 0
        self.comac_copied_files = 0
        self.total_runtime_seconds = 0
        self.cast_total_time = 0
        self.comac_total_time = 0

        # 初始化状态属性
        self.is_running = False
        self.is_cast_copying = False
        self.is_comac_copying = False
        self.start_time = None

        # 初始化选择框状态
        self.cast_enabled = True
        self.comac_enabled = True

        # 初始化监控变量
        self.processed_folders = set()
        self.pending_folders = []
        self.processing_folders = []
        self.last_folder_sizes = {}
        self.cast_pending_files = []
        self.comac_pending_files = []
        self.cast_waiting_seconds = 0
        self.comac_waiting_seconds = 0

        # 加载配置
        self.load_config()
        self.load_ac_type_config()

        # 创建UI
        self.init_ui()

        # 启动监控线程
        self.monitor_thread = None
        self.stop_monitoring = False

        # 设置窗口尺寸（参考Non_WGL_AUTO.py，稍微调大一点）
        window_width = int(650 * self.scale_factor)
        window_height = int(500 * self.scale_factor)
        self.setGeometry(100, 100, window_width, window_height)

        # 初始化定时器
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_countdown)
        self.update_timer.timeout.connect(self.update_file_counts)

    def setup_display_settings(self):
        """设置显示相关参数"""
        desktop = QDesktopWidget()
        screen_rect = desktop.screenGeometry()
        self.screen_width = screen_rect.width()
        self.screen_height = screen_rect.height()

        app = QApplication.instance()
        if app:
            screen = app.primaryScreen()
            dpi = screen.logicalDotsPerInch()
            self.scale_factor = max(1.0, dpi / 96.0)

            if self.screen_width >= 3840:
                self.scale_factor = max(self.scale_factor, 1.5)
            elif self.screen_width >= 2560:
                self.scale_factor = max(self.scale_factor, 1.25)
        else:
            self.scale_factor = 1.0

        self.base_font_size = max(10, int(11 * self.scale_factor))
        self.large_font_size = max(11, int(13 * self.scale_factor))

    def load_ac_type_config(self):
        """加载飞机型号配置文件"""
        try:
            # 首先尝试加载主配置文件
            if os.path.exists(self.ac_type_file):
                # 尝试多种编码方式
                encodings = ['utf-8-sig', 'utf-8', 'gbk', 'latin1']
                for encoding in encodings:
                    try:
                        with open(self.ac_type_file, 'r', encoding=encoding) as f:
                            self.ac_type_map = json.load(f)
                        logger.info(f"成功加载主配置文件（编码：{encoding}），共{len(self.ac_type_map)}架飞机")
                        return
                    except (UnicodeDecodeError, json.JSONDecodeError):
                        continue
                raise Exception("所有编码方式都失败")
            else:
                # 如果主配置文件不存在，尝试使用测试配置文件
                test_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "test_AC_TYPE.json")
                if os.path.exists(test_file):
                    with open(test_file, 'r', encoding='utf-8') as f:
                        self.ac_type_map = json.load(f)
                    logger.info(f"使用测试配置文件，共{len(self.ac_type_map)}架飞机")
                else:
                    self.ac_type_map = {}
                    logger.warning(f"配置文件不存在: {self.ac_type_file} 和 {test_file}")
        except Exception as e:
            self.ac_type_map = {}
            logger.error(f"加载飞机型号配置失败: {str(e)}")

    def is_arj21_aircraft(self, aircraft_code):
        """判断是否为ARJ21机型"""
        return self.ac_type_map.get(aircraft_code, '') == 'ARJ21'

    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.enc_file_count = config.get('enc_file_count', 0)
                    self.cast_copied_files = config.get('cast_copied_files', 0)
                    self.comac_copied_files = config.get('comac_copied_files', 0)
                    self.total_runtime_seconds = config.get('total_runtime_seconds', 0)
                    self.cast_total_time = config.get('cast_total_time', 0)
                    self.comac_total_time = config.get('comac_total_time', 0)
                    self.cast_enabled = config.get('cast_enabled', True)
                    self.comac_enabled = config.get('comac_enabled', True)
        except Exception as e:
            logger.error(f"加载配置文件出错: {str(e)}")

    def save_config(self):
        """保存配置文件"""
        try:
            config = {
                'enc_file_count': self.enc_file_count,
                'cast_copied_files': self.cast_copied_files,
                'comac_copied_files': self.comac_copied_files,
                'total_runtime_seconds': self.total_runtime_seconds,
                'cast_total_time': self.cast_total_time,
                'comac_total_time': self.comac_total_time,
                'cast_enabled': self.cast_enabled,
                'comac_enabled': self.comac_enabled,
                'last_run_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
        except Exception as e:
            logger.error(f"保存配置文件出错: {str(e)}")

    def init_ui(self):
        """创建简洁的PyQt5界面，参考Non_WGL_AUTO.py风格"""
        # 设置窗口样式（DPI自适应）
        padding_size = max(8, int(10 * self.scale_factor))
        border_radius = max(4, int(6 * self.scale_factor))
        margin_size = max(10, int(12 * self.scale_factor))

        self.setStyleSheet(f"""
            QMainWindow {{
                background-color: #f5f5f5;
            }}
            QGroupBox {{
                font-weight: bold;
                border: {max(1, int(2 * self.scale_factor))}px solid #cccccc;
                border-radius: {border_radius}px;
                margin-top: 1ex;
                padding-top: {margin_size}px;
                background-color: white;
                font-size: {self.base_font_size}px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: {margin_size}px;
                padding: 0 {padding_size}px 0 {padding_size}px;
                color: #2c3e50;
                font-size: {self.large_font_size}px;
                font-weight: bold;
            }}
            QLabel {{
                color: #2c3e50;
                font-size: {self.base_font_size}px;
            }}
            QLineEdit {{
                border: 1px solid #bdc3c7;
                border-radius: {max(3, int(4 * self.scale_factor))}px;
                padding: {padding_size}px;
                font-size: {self.base_font_size}px;
                background-color: white;
                min-height: {max(20, int(25 * self.scale_factor))}px;
            }}
            QLineEdit:focus {{
                border-color: #3498db;
                border-width: {max(1, int(2 * self.scale_factor))}px;
            }}
            QPushButton {{
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: {border_radius}px;
                padding: {padding_size}px {int(padding_size * 1.5)}px;
                font-size: {self.base_font_size}px;
                font-weight: bold;
                min-height: {max(25, int(30 * self.scale_factor))}px;
            }}
            QPushButton:hover {{
                background-color: #2980b9;
            }}
            QPushButton:pressed {{
                background-color: #21618c;
            }}
            QPushButton:disabled {{
                background-color: #bdc3c7;
                color: #7f8c8d;
            }}
            QCheckBox {{
                font-size: {self.base_font_size}px;
                color: #2c3e50;
            }}
            QCheckBox::indicator {{
                width: {max(16, int(18 * self.scale_factor))}px;
                height: {max(16, int(18 * self.scale_factor))}px;
            }}
            QCheckBox::indicator:unchecked {{
                border: 2px solid #bdc3c7;
                border-radius: 3px;
                background-color: white;
            }}
            QCheckBox::indicator:checked {{
                border: 2px solid #27ae60;
                border-radius: 3px;
                background-color: #27ae60;
            }}
        """)

        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)
        spacing = max(8, int(10 * self.scale_factor))
        margin = max(12, int(15 * self.scale_factor))
        main_layout.setSpacing(spacing)
        main_layout.setContentsMargins(margin, margin, margin, margin)

        # 路径设置分组
        path_group = QGroupBox("路径设置")
        path_layout = QVBoxLayout()
        path_layout.setSpacing(max(8, int(10 * self.scale_factor)))

        # DPI自适应的控件尺寸
        label_width = max(80, int(90 * self.scale_factor))
        input_height = max(25, int(30 * self.scale_factor))

        # 源路径
        source_layout = QHBoxLayout()
        source_label = QLabel("源文件夹:")
        source_label.setMinimumWidth(label_width)
        source_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.source_edit = QLineEdit(self.path1)
        self.source_edit.setReadOnly(True)
        self.source_edit.setMinimumHeight(input_height)
        source_layout.addWidget(source_label)
        source_layout.addWidget(self.source_edit, 1)

        # 目标路径
        target_layout = QHBoxLayout()
        target_label = QLabel("目标文件夹:")
        target_label.setMinimumWidth(label_width)
        target_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.target_edit = QLineEdit(self.path2)
        self.target_edit.setReadOnly(True)
        self.target_edit.setMinimumHeight(input_height)
        target_layout.addWidget(target_label)
        target_layout.addWidget(self.target_edit, 1)

        path_layout.addLayout(source_layout)
        path_layout.addLayout(target_layout)
        path_group.setLayout(path_layout)

        # 复制选项分组
        option_group = QGroupBox("复制选项")
        option_layout = QHBoxLayout()

        self.cast_checkbox = QCheckBox("CAST数据复制")
        self.cast_checkbox.setChecked(self.cast_enabled)
        self.cast_checkbox.stateChanged.connect(self.on_cast_checkbox_changed)

        self.comac_checkbox = QCheckBox("COMAC数据复制")
        self.comac_checkbox.setChecked(self.comac_enabled)
        self.comac_checkbox.stateChanged.connect(self.on_comac_checkbox_changed)

        option_layout.addWidget(self.cast_checkbox)
        option_layout.addWidget(self.comac_checkbox)
        option_layout.addStretch()
        option_group.setLayout(option_layout)

        main_layout.addWidget(path_group)
        main_layout.addWidget(option_group)

        # 状态信息分组
        status_group = QGroupBox("复制状态")
        status_layout = QVBoxLayout()
        status_layout.setSpacing(10)

        # CAST状态
        cast_layout = QHBoxLayout()
        cast_title = QLabel("CAST:")
        cast_title.setStyleSheet(f"QLabel {{ font-weight: bold; color: #3498db; font-size: {self.base_font_size}px; min-width: 60px; }}")
        self.cast_status_label = QLabel("等待开始...")
        self.cast_status_label.setAlignment(Qt.AlignLeft)
        info_padding = max(6, int(8 * self.scale_factor))
        info_radius = max(3, int(4 * self.scale_factor))
        self.cast_status_label.setStyleSheet(f"""
            QLabel {{
                font-size: {self.base_font_size}px;
                color: #34495e;
                padding: {info_padding}px;
                background-color: #ecf0f1;
                border-radius: {info_radius}px;
                margin: 2px;
            }}
        """)

        self.cast_copied_label = QLabel("已复制: 0")
        self.cast_copied_label.setStyleSheet(f"QLabel {{ font-weight: bold; color: #27ae60; font-size: {self.base_font_size}px; min-width: 80px; }}")

        self.cast_pending_label = QLabel("待复制: 0")
        self.cast_pending_label.setStyleSheet(f"QLabel {{ font-weight: bold; color: #e74c3c; font-size: {self.base_font_size}px; min-width: 80px; }}")

        cast_layout.addWidget(cast_title)
        cast_layout.addWidget(self.cast_status_label, 1)
        cast_layout.addWidget(self.cast_copied_label)
        cast_layout.addWidget(self.cast_pending_label)

        # COMAC状态
        comac_layout = QHBoxLayout()
        comac_title = QLabel("COMAC:")
        comac_title.setStyleSheet(f"QLabel {{ font-weight: bold; color: #e67e22; font-size: {self.base_font_size}px; min-width: 60px; }}")
        self.comac_status_label = QLabel("等待开始...")
        self.comac_status_label.setAlignment(Qt.AlignLeft)
        self.comac_status_label.setStyleSheet(f"""
            QLabel {{
                font-size: {self.base_font_size}px;
                color: #34495e;
                padding: {info_padding}px;
                background-color: #ecf0f1;
                border-radius: {info_radius}px;
                margin: 2px;
            }}
        """)

        self.comac_copied_label = QLabel("已复制: 0")
        self.comac_copied_label.setStyleSheet(f"QLabel {{ font-weight: bold; color: #27ae60; font-size: {self.base_font_size}px; min-width: 80px; }}")

        self.comac_pending_label = QLabel("待复制: 0")
        self.comac_pending_label.setStyleSheet(f"QLabel {{ font-weight: bold; color: #e74c3c; font-size: {self.base_font_size}px; min-width: 80px; }}")

        comac_layout.addWidget(comac_title)
        comac_layout.addWidget(self.comac_status_label, 1)
        comac_layout.addWidget(self.comac_copied_label)
        comac_layout.addWidget(self.comac_pending_label)

        # 总体统计
        self.stats_label = QLabel(f"ENC文件数: {self.enc_file_count}")
        self.stats_label.setAlignment(Qt.AlignCenter)
        time_padding = max(4, int(5 * self.scale_factor))
        self.stats_label.setStyleSheet(f"""
            QLabel {{
                font-size: {self.base_font_size}px;
                color: #7f8c8d;
                padding: {time_padding}px;
            }}
        """)

        status_layout.addLayout(cast_layout)
        status_layout.addLayout(comac_layout)
        status_layout.addWidget(self.stats_label)
        status_group.setLayout(status_layout)

        main_layout.addWidget(status_group)

        # 等待时间显示分组（完全按照截图的上下结构）
        countdown_group = QGroupBox("等待时间")
        countdown_main_layout = QVBoxLayout()
        countdown_main_layout.setSpacing(max(10, int(15 * self.scale_factor)))

        # 第一行：标题行
        title_layout = QHBoxLayout()
        title_layout.setAlignment(Qt.AlignCenter)
        title_layout.setSpacing(max(50, int(80 * self.scale_factor)))

        total_runtime_title = QLabel("总运行时长")
        total_runtime_title.setAlignment(Qt.AlignCenter)
        total_runtime_title.setStyleSheet(f"""
            QLabel {{
                color: #7f8c8d;
                font-size: {self.base_font_size}px;
            }}
        """)

        current_wait_title = QLabel("当前等待时间")
        current_wait_title.setAlignment(Qt.AlignCenter)
        current_wait_title.setStyleSheet(f"""
            QLabel {{
                color: #7f8c8d;
                font-size: {self.base_font_size}px;
            }}
        """)

        avg_wait_title = QLabel("平均等待时间")
        avg_wait_title.setAlignment(Qt.AlignCenter)
        avg_wait_title.setStyleSheet(f"""
            QLabel {{
                color: #7f8c8d;
                font-size: {self.base_font_size}px;
            }}
        """)

        title_layout.addWidget(total_runtime_title)
        title_layout.addWidget(current_wait_title)
        title_layout.addWidget(avg_wait_title)

        # 第二行：数字框行
        value_layout = QHBoxLayout()
        value_layout.setAlignment(Qt.AlignCenter)
        value_layout.setSpacing(max(15, int(20 * self.scale_factor)))

        # 总运行时长框（绿色）
        self.total_runtime_label = QLabel("0天15:27:37")
        self.total_runtime_label.setAlignment(Qt.AlignCenter)
        self.total_runtime_label.setStyleSheet(f"""
            QLabel {{
                color: #28a745;
                background-color: #d4edda;
                border: 2px solid #c3e6cb;
                border-radius: {max(8, int(10 * self.scale_factor))}px;
                padding: {max(15, int(20 * self.scale_factor))}px;
                font-size: {max(18, int(24 * self.scale_factor))}px;
                font-weight: bold;
                min-width: {max(120, int(150 * self.scale_factor))}px;
                min-height: {max(50, int(60 * self.scale_factor))}px;
            }}
        """)

        # 当前等待时间框（红色）
        self.current_wait_label = QLabel("00:49")
        self.current_wait_label.setAlignment(Qt.AlignCenter)
        self.current_wait_label.setStyleSheet(f"""
            QLabel {{
                color: #dc3545;
                background-color: #f8d7da;
                border: 2px solid #f5c6cb;
                border-radius: {max(8, int(10 * self.scale_factor))}px;
                padding: {max(15, int(20 * self.scale_factor))}px;
                font-size: {max(18, int(24 * self.scale_factor))}px;
                font-weight: bold;
                min-width: {max(80, int(100 * self.scale_factor))}px;
                min-height: {max(50, int(60 * self.scale_factor))}px;
            }}
        """)

        # 平均等待时间框（蓝色）
        self.avg_wait_label = QLabel("01:39")
        self.avg_wait_label.setAlignment(Qt.AlignCenter)
        self.avg_wait_label.setStyleSheet(f"""
            QLabel {{
                color: #17a2b8;
                background-color: #d1ecf1;
                border: 2px solid #bee5eb;
                border-radius: {max(8, int(10 * self.scale_factor))}px;
                padding: {max(15, int(20 * self.scale_factor))}px;
                font-size: {max(18, int(24 * self.scale_factor))}px;
                font-weight: bold;
                min-width: {max(80, int(100 * self.scale_factor))}px;
                min-height: {max(50, int(60 * self.scale_factor))}px;
            }}
        """)

        value_layout.addWidget(self.total_runtime_label)
        value_layout.addWidget(self.current_wait_label)
        value_layout.addWidget(self.avg_wait_label)

        countdown_main_layout.addLayout(title_layout)
        countdown_main_layout.addLayout(value_layout)
        countdown_group.setLayout(countdown_main_layout)

        # 控制按钮分组
        control_group = QGroupBox("操作控制")
        control_layout = QHBoxLayout()
        control_layout.setSpacing(max(10, int(12 * self.scale_factor)))

        # DPI自适应的按钮尺寸
        button_height = max(32, int(38 * self.scale_factor))
        button_width = max(90, int(110 * self.scale_factor))
        button_font_size = max(11, int(13 * self.scale_factor))

        self.start_button = QPushButton("🚀 开始监控")
        self.start_button.setMinimumHeight(button_height)
        self.start_button.setMinimumWidth(button_width)
        self.start_button.setStyleSheet(f"""
            QPushButton {{
                background-color: #27ae60;
                font-size: {button_font_size}px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #229954;
            }}
            QPushButton:pressed {{
                background-color: #1e8449;
            }}
        """)
        self.start_button.clicked.connect(self.start_monitoring)

        self.stop_button = QPushButton("⏹ 暂停监控")
        self.stop_button.setMinimumHeight(button_height)
        self.stop_button.setMinimumWidth(button_width)
        self.stop_button.setStyleSheet(f"""
            QPushButton {{
                background-color: #e74c3c;
                font-size: {button_font_size}px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #c0392b;
            }}
            QPushButton:pressed {{
                background-color: #a93226;
            }}
            QPushButton:disabled {{
                background-color: #bdc3c7;
                color: #7f8c8d;
            }}
        """)
        self.stop_button.clicked.connect(self.stop_monitoring_func)
        self.stop_button.setEnabled(False)

        self.exit_button = QPushButton("❌ 退出程序")
        self.exit_button.setMinimumHeight(button_height)
        self.exit_button.setMinimumWidth(button_width)
        self.exit_button.setStyleSheet(f"""
            QPushButton {{
                background-color: #95a5a6;
                font-size: {button_font_size}px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #7f8c8d;
            }}
            QPushButton:pressed {{
                background-color: #6c7b7d;
            }}
        """)
        self.exit_button.clicked.connect(self.exit_application)

        control_layout.addStretch()
        control_layout.addWidget(self.start_button)
        control_layout.addWidget(self.stop_button)
        control_layout.addWidget(self.exit_button)
        control_layout.addStretch()
        control_group.setLayout(control_layout)

        main_layout.addWidget(countdown_group)
        main_layout.addWidget(control_group)

        # 初始化显示
        self.update_stats_display()
        self.update_avg_wait_display()
        self.update_file_counts()
        self.update_total_runtime()



    def on_cast_checkbox_changed(self, state):
        """CAST选择框状态改变"""
        self.cast_enabled = state == Qt.Checked

    def on_comac_checkbox_changed(self, state):
        """COMAC选择框状态改变"""
        self.comac_enabled = state == Qt.Checked

    def update_stats_display(self):
        """更新统计信息显示"""
        self.stats_label.setText(f"ENC文件数: {self.enc_file_count}")

    def update_file_counts(self):
        """更新文件数量显示"""
        try:
            # 更新CAST文件数量
            cast_pending = 0
            if os.path.exists(self.cast_source):
                cast_pending = len([f for f in os.listdir(self.cast_source)
                                  if f.lower().endswith('.zip')])

            self.cast_copied_label.setText(f"已复制: {self.cast_copied_files}")
            self.cast_pending_label.setText(f"待复制: {cast_pending}")

            # 更新COMAC文件数量
            comac_pending = 0
            if os.path.exists(self.comac_source):
                comac_pending = len([f for f in os.listdir(self.comac_source)
                                   if f.lower().endswith('.zip')])

            self.comac_copied_label.setText(f"已复制: {self.comac_copied_files}")
            self.comac_pending_label.setText(f"待复制: {comac_pending}")

        except Exception as e:
            logger.error(f"更新文件数量显示出错: {str(e)}")

    def update_total_runtime(self):
        """更新总运行时长显示"""
        if self.is_running and self.start_time:
            # 计算当前运行时长
            current_runtime = (datetime.now() - self.start_time).total_seconds()
            total_seconds = int(self.total_runtime_seconds + current_runtime)
        else:
            total_seconds = int(self.total_runtime_seconds)

        days = total_seconds // 86400
        hours = (total_seconds % 86400) // 3600
        minutes = (total_seconds % 3600) // 60
        seconds = total_seconds % 60
        self.total_runtime_label.setText(f"{days}天{hours:02d}:{minutes:02d}:{seconds:02d}")

    def exit_application(self):
        """退出程序"""
        # 停止监控
        if self.is_running:
            self.stop_monitoring_func()

        # 保存配置
        self.save_config()

        # 关闭程序
        QApplication.quit()

    def update_avg_wait_display(self):
        """更新平均等待时间显示"""
        total_copied = self.cast_copied_files + self.comac_copied_files
        if total_copied > 0:
            total_time = self.cast_total_time + self.comac_total_time
            avg_seconds = total_time / total_copied
            avg_minutes = int(avg_seconds // 60)
            avg_secs = int(avg_seconds % 60)
            self.avg_wait_label.setText(f"{avg_minutes:02d}:{avg_secs:02d}")
        else:
            self.avg_wait_label.setText("00:00")

    def get_folder_size(self, folder_path):
        """计算文件夹大小"""
        total_size = 0
        try:
            for dirpath, dirnames, filenames in os.walk(folder_path):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    if os.path.exists(filepath):
                        total_size += os.path.getsize(filepath)
        except Exception as e:
            logger.error(f"计算文件夹大小出错: {str(e)}")
        return total_size

    def create_zip_file(self, source_folder, zip_path):
        """创建ZIP文件，只包含文件夹内的文件，不包含文件夹本身"""
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(source_folder):
                for file in files:
                    file_path = os.path.join(root, file)
                    # 计算相对路径，不包含源文件夹名
                    arcname = os.path.relpath(file_path, source_folder)
                    zipf.write(file_path, arcname)

    def process_folder(self, folder_name):
        """处理新文件夹：压缩并分类"""
        try:
            # 提取飞机号（前6个字符）
            aircraft_code = folder_name[:6].upper()
            logger.info(f"处理文件夹: {folder_name}, 飞机号: {aircraft_code}")

            # 创建目标目录
            os.makedirs(self.cast_source, exist_ok=True)
            os.makedirs(self.comac_source, exist_ok=True)

            # 压缩文件夹
            zip_filename = f"{folder_name}M.zip"
            source_folder_path = os.path.join(self.path1, folder_name)
            cast_zip_path = os.path.join(self.cast_source, zip_filename)

            self.create_zip_file(source_folder_path, cast_zip_path)
            logger.info(f"创建压缩文件: {cast_zip_path}")

            # 判断是否为ARJ21机型
            if self.is_arj21_aircraft(aircraft_code):
                comac_zip_path = os.path.join(self.comac_source, zip_filename)
                shutil.copy2(cast_zip_path, comac_zip_path)
                logger.info(f"ARJ21机型，复制到COMAC: {comac_zip_path}")

            # 更新统计
            self.enc_file_count += 1
            self.processed_folders.add(folder_name)
            self.update_stats_display()
            self.update_file_counts()
            self.save_config()

            logger.info(f"文件夹 {folder_name} 处理完成")

        except Exception as e:
            logger.error(f"处理文件夹 {folder_name} 出错: {str(e)}")

    def start_monitoring(self):
        """开始监控"""
        # 确保必要的目录存在
        try:
            os.makedirs(self.path1, exist_ok=True)
            os.makedirs(self.path2, exist_ok=True)
            os.makedirs(self.cast_source, exist_ok=True)
            os.makedirs(self.comac_source, exist_ok=True)
            os.makedirs(self.cast_target, exist_ok=True)
            os.makedirs(self.comac_target, exist_ok=True)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法创建必要目录: {str(e)}")
            return

        self.is_running = True
        self.start_time = datetime.now()
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.stop_monitoring = False

        self.cast_status_label.setText("监控中...")
        self.comac_status_label.setText("监控中...")

        # 启动监控线程
        self.monitor_thread = threading.Thread(target=self.monitoring_loop, daemon=True)
        self.monitor_thread.start()

        # 启动UI更新定时器
        self.update_timer.start(1000)

        logger.info("开始监控")

    def stop_monitoring_func(self):
        """停止监控"""
        self.is_running = False
        self.stop_monitoring = True

        # 更新总运行时长
        if self.start_time:
            current_runtime = (datetime.now() - self.start_time).total_seconds()
            self.total_runtime_seconds += current_runtime
            self.start_time = None

        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)

        self.cast_status_label.setText("已停止")
        self.comac_status_label.setText("已停止")

        # 停止定时器
        self.update_timer.stop()

        # 更新显示
        self.update_total_runtime()

        # 保存配置
        self.save_config()

        logger.info("监控已停止")

    def update_countdown(self):
        """更新等待时间显示"""
        if not self.is_running:
            return

        try:
            # 优先显示CAST的等待时间，如果CAST没有文件则显示COMAC的
            cast_files = len([f for f in os.listdir(self.cast_source) if f.lower().endswith('.zip')]) if os.path.exists(self.cast_source) else 0
            comac_files = len([f for f in os.listdir(self.comac_source) if f.lower().endswith('.zip')]) if os.path.exists(self.comac_source) else 0

            if cast_files > 0 and self.cast_enabled:
                self.cast_waiting_seconds += 1
                minutes = self.cast_waiting_seconds // 60
                seconds = self.cast_waiting_seconds % 60
                self.current_wait_label.setText(f"{minutes:02d}:{seconds:02d}")
            elif comac_files > 0 and self.comac_enabled:
                self.comac_waiting_seconds += 1
                minutes = self.comac_waiting_seconds // 60
                seconds = self.comac_waiting_seconds % 60
                self.current_wait_label.setText(f"{minutes:02d}:{seconds:02d}")
            else:
                self.current_wait_label.setText("00:00")

        except Exception as e:
            logger.error(f"更新等待时间显示出错: {str(e)}")

        # 更新文件数量和总运行时长
        self.update_file_counts()
        self.update_total_runtime()

    def monitoring_loop(self):
        """监控循环"""
        while self.is_running and not self.stop_monitoring:
            try:
                # 监控源文件夹
                self.monitor_source_folder()

                # 检查目标文件夹
                if self.cast_enabled.get():
                    self.check_cast_folder_empty()

                if self.comac_enabled.get():
                    self.check_comac_folder_empty()

                time.sleep(5)  # 每5秒检查一次

            except Exception as e:
                logger.error(f"监控循环出错: {str(e)}")
                time.sleep(5)

    def monitor_source_folder(self):
        """监控源文件夹，检测新文件夹"""
        if not self.is_running:
            return

        try:
            # 获取所有文件夹
            if not os.path.exists(self.path1):
                return

            current_folders = [f for f in os.listdir(self.path1)
                             if os.path.isdir(os.path.join(self.path1, f)) and not f.startswith('.')]

            # 检查新文件夹
            for folder in current_folders:
                if folder not in self.processed_folders and folder not in self.processing_folders:
                    self.processing_folders.append(folder)
                    self.check_folder_write_complete(folder)

        except Exception as e:
            logger.error(f"监控源文件夹出错: {str(e)}")

    def check_folder_write_complete(self, folder):
        """检查文件夹是否写入完成"""
        if not self.is_running:
            return

        folder_path = os.path.join(self.path1, folder)
        try:
            if not os.path.exists(folder_path):
                if folder in self.processing_folders:
                    self.processing_folders.remove(folder)
                return

            # 计算文件夹大小
            current_size = self.get_folder_size(folder_path)

            if folder in self.last_folder_sizes:
                # 如果文件夹大小没有变化，认为写入完成
                if current_size == self.last_folder_sizes[folder]:
                    if folder in self.processing_folders:
                        self.processing_folders.remove(folder)
                    self.process_folder(folder)
                    return
                else:
                    self.last_folder_sizes[folder] = current_size
            else:
                self.last_folder_sizes[folder] = current_size

            # 5秒后再次检查
            time.sleep(5)
            if self.is_running:
                self.check_folder_write_complete(folder)

        except Exception as e:
            logger.error(f"检查文件夹写入完成出错: {str(e)}")
            if folder in self.processing_folders:
                self.processing_folders.remove(folder)

    def check_cast_folder_empty(self):
        """检查CAST目标文件夹是否为空"""
        if not self.is_running or not self.cast_enabled.get() or self.is_cast_copying:
            return

        try:
            if not os.path.exists(self.cast_target):
                return

            target_files = [f for f in os.listdir(self.cast_target) if not f.startswith('.')]

            if len(target_files) == 0:  # 文件夹为空
                # 检查是否有待复制文件
                if os.path.exists(self.cast_source):
                    source_files = [f for f in os.listdir(self.cast_source) if f.lower().endswith('.zip')]

                    if source_files and not self.is_cast_copying:
                        self.is_cast_copying = True
                        self.copy_cast_files(source_files[0])

        except Exception as e:
            logger.error(f"检查CAST文件夹出错: {str(e)}")

    def check_comac_folder_empty(self):
        """检查COMAC目标文件夹是否为空"""
        if not self.is_running or not self.comac_enabled.get() or self.is_comac_copying:
            return

        try:
            if not os.path.exists(self.comac_target):
                return

            target_files = [f for f in os.listdir(self.comac_target) if not f.startswith('.')]

            if len(target_files) == 0:  # 文件夹为空
                # 检查是否有待复制文件
                if os.path.exists(self.comac_source):
                    source_files = [f for f in os.listdir(self.comac_source) if f.lower().endswith('.zip')]

                    if source_files and not self.is_comac_copying:
                        self.is_comac_copying = True
                        self.copy_comac_files(source_files[0])

        except Exception as e:
            logger.error(f"检查COMAC文件夹出错: {str(e)}")

    def copy_cast_files(self, file_to_copy):
        """复制CAST文件"""
        try:
            source_path = os.path.join(self.cast_source, file_to_copy)
            target_path = os.path.join(self.cast_target, file_to_copy)

            self.cast_status_label.setText(f"正在复制 {file_to_copy}")

            copy_start_time = time.time()

            # 确保目标目录存在
            os.makedirs(self.cast_target, exist_ok=True)

            # 复制文件
            shutil.copy2(source_path, target_path)

            # 移动源文件到已复制目录
            copied_dir = os.path.join(self.cast_source, "Copied")
            os.makedirs(copied_dir, exist_ok=True)
            shutil.move(source_path, os.path.join(copied_dir, file_to_copy))

            # 更新统计
            copy_duration = time.time() - copy_start_time
            self.cast_total_time += copy_duration
            self.cast_copied_files += 1

            # 重置等待时间
            self.cast_waiting_seconds = 0

            self.cast_status_label.setText(f"复制完成 {file_to_copy}")
            self.update_stats_display()
            self.update_avg_wait_display()
            self.update_file_counts()
            self.save_config()

            logger.info(f"CAST文件复制完成: {file_to_copy}")

        except Exception as e:
            self.cast_status_label.setText(f"复制失败 {str(e)}")
            logger.error(f"CAST文件复制失败: {str(e)}")

        finally:
            self.is_cast_copying = False

    def copy_comac_files(self, file_to_copy):
        """复制COMAC文件"""
        try:
            source_path = os.path.join(self.comac_source, file_to_copy)
            target_path = os.path.join(self.comac_target, file_to_copy)

            self.comac_status_label.setText(f"正在复制 {file_to_copy}")

            copy_start_time = time.time()

            # 确保目标目录存在
            os.makedirs(self.comac_target, exist_ok=True)

            # 复制文件
            shutil.copy2(source_path, target_path)

            # 移动源文件到已复制目录
            copied_dir = os.path.join(self.comac_source, "Copied")
            os.makedirs(copied_dir, exist_ok=True)
            shutil.move(source_path, os.path.join(copied_dir, file_to_copy))

            # 更新统计
            copy_duration = time.time() - copy_start_time
            self.comac_total_time += copy_duration
            self.comac_copied_files += 1

            # 重置等待时间
            self.comac_waiting_seconds = 0

            self.comac_status_label.setText(f"复制完成 {file_to_copy}")
            self.update_stats_display()
            self.update_avg_wait_display()
            self.update_file_counts()
            self.save_config()

            logger.info(f"COMAC文件复制完成: {file_to_copy}")

        except Exception as e:
            self.comac_status_label.setText(f"复制失败 {str(e)}")
            logger.error(f"COMAC文件复制失败: {str(e)}")

        finally:
            self.is_comac_copying = False

def setup_high_dpi_support():
    """设置高DPI支持"""
    if hasattr(Qt, 'AA_EnableHighDpiScaling'):
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

if __name__ == "__main__":
    setup_high_dpi_support()

    app = QApplication(sys.argv)

    # 设置应用程序样式
    app.setStyle('Fusion')

    # 创建并显示窗口
    window = ENCAutoXferApp()
    window.show()

    # 居中显示窗口
    desktop = QDesktopWidget()
    screen_rect = desktop.screenGeometry()
    window_rect = window.geometry()
    x = (screen_rect.width() - window_rect.width()) // 2
    y = (screen_rect.height() - window_rect.height()) // 2
    window.move(x, y)

    # 程序关闭时保存配置
    def cleanup():
        window.stop_monitoring_func()
        window.save_config()

    app.aboutToQuit.connect(cleanup)

    sys.exit(app.exec_())