import os
import shutil
import sys
import json
import time
from datetime import datetime
from collections import deque
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                             QPushButton, QLabel, QLineEdit, QFileDialog, QWidget,
                             QProgressBar, QMessageBox, QGroupBox, QFrame, QDesktopWidget)
from PyQt5.QtCore import QTimer, Qt, QFileSystemWatcher
from PyQt5.QtGui import QFont, QPalette, QFontDatabase, QIcon


class FileCopyApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Non_WGL_AUTO ZIP文件定时复制工具")

        # 获取屏幕信息和DPI缩放
        self.setup_display_settings()

        # 设置窗口尺寸（根据DPI自适应，调整为更紧凑的尺寸）
        window_width = int(580 * self.scale_factor)
        window_height = int(420 * self.scale_factor)
        self.setGeometry(100, 100, window_width, window_height)

        # 设置窗口样式（DPI自适应）
        padding_size = max(8, int(10 * self.scale_factor))
        border_radius = max(4, int(6 * self.scale_factor))
        margin_size = max(10, int(12 * self.scale_factor))

        self.setStyleSheet(f"""
            QMainWindow {{
                background-color: #f5f5f5;
            }}
            QGroupBox {{
                font-weight: bold;
                border: {max(1, int(2 * self.scale_factor))}px solid #cccccc;
                border-radius: {border_radius}px;
                margin-top: 1ex;
                padding-top: {margin_size}px;
                background-color: white;
                font-size: {self.base_font_size}px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: {margin_size}px;
                padding: 0 {padding_size}px 0 {padding_size}px;
                color: #2c3e50;
                font-size: {self.large_font_size}px;
                font-weight: bold;
            }}
            QLabel {{
                color: #2c3e50;
                font-size: {self.base_font_size}px;
            }}
            QLineEdit {{
                border: 1px solid #bdc3c7;
                border-radius: {max(3, int(4 * self.scale_factor))}px;
                padding: {padding_size}px;
                font-size: {self.base_font_size}px;
                background-color: white;
                min-height: {max(20, int(25 * self.scale_factor))}px;
            }}
            QLineEdit:focus {{
                border-color: #3498db;
                border-width: {max(1, int(2 * self.scale_factor))}px;
            }}
            QPushButton {{
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: {border_radius}px;
                padding: {padding_size}px {int(padding_size * 1.5)}px;
                font-size: {self.base_font_size}px;
                font-weight: bold;
                min-height: {max(25, int(30 * self.scale_factor))}px;
            }}
            QPushButton:hover {{
                background-color: #2980b9;
            }}
            QPushButton:pressed {{
                background-color: #21618c;
            }}
            QPushButton:disabled {{
                background-color: #bdc3c7;
                color: #7f8c8d;
            }}
            QProgressBar {{
                border: 1px solid #bdc3c7;
                border-radius: {border_radius}px;
                text-align: center;
                font-size: {self.base_font_size}px;
                font-weight: bold;
                background-color: #ecf0f1;
                min-height: {max(20, int(25 * self.scale_factor))}px;
            }}
            QProgressBar::chunk {{
                background-color: #27ae60;
                border-radius: {max(3, int(5 * self.scale_factor))}px;
            }}
        """)

        # 初始化变量
        self.source_path = "E:\\ZIP"
        self.target_path = "D:\\NON-WGL\\CAST"
        self.file_queue = deque()  # 待复制文件队列
        self.copied_files_count = 0  # 已复制文件数
        self.total_runtime = 0  # 总运行时长（秒）
        self.total_copy_time = 0  # 总复制文件时长（秒）- 只在有复制任务时计时
        self.start_time = None
        self.current_file_start_time = None
        self.copy_task_start_time = None  # 复制任务开始时间
        self.is_running = False
        self.is_paused = False
        self.is_copy_task_active = False  # 是否有活跃的复制任务
        self.waiting_seconds = 0  # 当前文件等待时间
        self.average_wait_time = 0  # 平均等待时长

        # 定时器
        self.countdown_timer = QTimer()
        self.countdown_timer.timeout.connect(self.update_countdown)
        self.folder_check_timer = QTimer()
        self.folder_check_timer.timeout.connect(self.check_target_folder)
        self.file_monitor_timer = QTimer()
        self.file_monitor_timer.timeout.connect(self.monitor_source_folder)
        self.runtime_update_timer = QTimer()
        self.runtime_update_timer.timeout.connect(self.update_runtime_display)
        self.runtime_update_timer.start(1000)  # 每秒更新一次总运行时长显示

        # 文件系统监控
        self.file_watcher = QFileSystemWatcher()
        self.file_watcher.directoryChanged.connect(self.on_directory_changed)

        # 文件完整性检查
        self.pending_files = {}  # 待检查文件 {文件路径: {'size': size, 'mtime': mtime, 'check_count': count, 'start_time': time}}
        self.file_check_timer = QTimer()
        self.file_check_timer.timeout.connect(self.check_file_integrity)

        # 加载历史数据
        self.load_history_data()

        # 设置程序图标
        self.set_app_icon()

        # 创建UI
        self.init_ui()

        # 开始监控源文件夹
        self.start_monitoring()

    def set_app_icon(self):
        """设置程序图标"""
        try:
            program_dir = self.get_program_directory()
            icon_path = os.path.join(program_dir, "app.ico")
            if os.path.exists(icon_path):
                icon = QIcon(icon_path)
                self.setWindowIcon(icon)
                # 设置应用程序图标（任务栏图标）
                QApplication.instance().setWindowIcon(icon)
                print(f"设置程序图标: {icon_path}")
            else:
                print(f"图标文件不存在: {icon_path}")
        except Exception as e:
            print(f"设置图标失败: {str(e)}")

    def setup_display_settings(self):
        """设置显示相关参数"""
        # 获取屏幕信息
        desktop = QDesktopWidget()
        screen_rect = desktop.screenGeometry()
        self.screen_width = screen_rect.width()
        self.screen_height = screen_rect.height()

        # 计算DPI缩放因子
        app = QApplication.instance()
        if app:
            # 获取主屏幕的DPI
            screen = app.primaryScreen()
            dpi = screen.logicalDotsPerInch()
            # 标准DPI是96，计算缩放因子
            self.scale_factor = max(1.0, dpi / 96.0)

            # 对于4K显示器，进一步调整
            if self.screen_width >= 3840:  # 4K或更高分辨率
                self.scale_factor = max(self.scale_factor, 1.5)
            elif self.screen_width >= 2560:  # 2K分辨率
                self.scale_factor = max(self.scale_factor, 1.25)
        else:
            self.scale_factor = 1.0

        # 计算基础字体大小（字体大一号）
        self.base_font_size = max(10, int(11 * self.scale_factor))
        self.large_font_size = max(11, int(13 * self.scale_factor))
        self.huge_font_size = max(36, int(44 * self.scale_factor))

        print(f"屏幕分辨率: {self.screen_width}x{self.screen_height}")
        print(f"DPI缩放因子: {self.scale_factor:.2f}")
        print(f"字体大小: 基础={self.base_font_size}, 大={self.large_font_size}, 超大={self.huge_font_size}")

    def init_ui(self):
        # 主窗口布局
        main_widget = QWidget()
        main_layout = QVBoxLayout()

        # DPI自适应的间距和边距（调整为更紧凑）
        spacing = max(8, int(10 * self.scale_factor))
        margin = max(12, int(15 * self.scale_factor))

        main_layout.setSpacing(spacing)
        main_layout.setContentsMargins(margin, margin, margin, margin)

        # 路径设置分组
        path_group = QGroupBox("路径设置")
        path_layout = QVBoxLayout()
        path_layout.setSpacing(max(8, int(10 * self.scale_factor)))

        # DPI自适应的控件尺寸（调整为更紧凑）
        label_width = max(60, int(70 * self.scale_factor))
        button_width = max(60, int(70 * self.scale_factor))
        input_height = max(25, int(30 * self.scale_factor))
        button_height = max(25, int(30 * self.scale_factor))

        # 源路径设置
        source_layout = QHBoxLayout()
        source_label = QLabel("源文件夹:")
        source_label.setMinimumWidth(label_width)
        source_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.source_edit = QLineEdit(self.source_path)
        self.source_edit.setReadOnly(True)
        self.source_edit.setMinimumHeight(input_height)
        source_button = QPushButton("浏览...")
        source_button.setMinimumWidth(button_width)
        source_button.setMinimumHeight(button_height)
        source_button.clicked.connect(self.select_source_folder)
        source_layout.addWidget(source_label)
        source_layout.addWidget(self.source_edit, 1)
        source_layout.addWidget(source_button)

        # 目标路径设置
        target_layout = QHBoxLayout()
        target_label = QLabel("目标文件夹:")
        target_label.setMinimumWidth(label_width)
        target_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.target_edit = QLineEdit(self.target_path)
        self.target_edit.setReadOnly(True)
        self.target_edit.setMinimumHeight(input_height)
        target_button = QPushButton("浏览...")
        target_button.setMinimumWidth(button_width)
        target_button.setMinimumHeight(button_height)
        target_button.clicked.connect(self.select_target_folder)
        target_layout.addWidget(target_label)
        target_layout.addWidget(self.target_edit, 1)
        target_layout.addWidget(target_button)

        path_layout.addLayout(source_layout)
        path_layout.addLayout(target_layout)
        path_group.setLayout(path_layout)

        # 状态信息分组
        status_group = QGroupBox("复制状态")
        status_layout = QVBoxLayout()
        status_layout.setSpacing(10)

        # 文件统计信息
        self.file_stats_label = QLabel("监控中...")
        self.file_stats_label.setAlignment(Qt.AlignCenter)
        info_padding = max(6, int(8 * self.scale_factor))
        info_radius = max(3, int(4 * self.scale_factor))
        self.file_stats_label.setStyleSheet(f"""
            QLabel {{
                font-size: {self.large_font_size}px;
                font-weight: bold;
                color: #34495e;
                padding: {info_padding}px;
                background-color: #ecf0f1;
                border-radius: {info_radius}px;
            }}
        """)

        # 时间统计信息
        self.time_stats_label = QLabel("预计剩余时间: --:--:-- | 最后操作时间: --:--:--")
        self.time_stats_label.setAlignment(Qt.AlignCenter)
        time_padding = max(4, int(5 * self.scale_factor))
        self.time_stats_label.setStyleSheet(f"""
            QLabel {{
                font-size: {max(12, int(14 * self.scale_factor))}px;
                color: #7f8c8d;
                padding: {time_padding}px;
                font-weight: bold;
            }}
        """)

        # 完成状态信息
        self.completion_label = QLabel("")
        self.completion_label.setAlignment(Qt.AlignCenter)
        self.completion_label.setVisible(False)
        completion_padding = max(8, int(10 * self.scale_factor))
        completion_radius = max(4, int(6 * self.scale_factor))
        self.completion_label.setStyleSheet(f"""
            QLabel {{
                font-size: {self.large_font_size}px;
                font-weight: bold;
                color: #27ae60;
                padding: {completion_padding}px;
                background-color: #d5f4e6;
                border: 2px solid #27ae60;
                border-radius: {completion_radius}px;
                margin: 5px;
            }}
        """)

        status_layout.addWidget(self.file_stats_label)
        status_layout.addWidget(self.time_stats_label)
        status_layout.addWidget(self.completion_label)
        status_group.setLayout(status_layout)

        # 等待时间显示分组
        countdown_group = QGroupBox("等待时间")
        countdown_layout = QVBoxLayout()
        countdown_layout.setAlignment(Qt.AlignCenter)

        # 三栏布局：总运行时长、当前等待时间、平均等待时间
        wait_time_layout = QHBoxLayout()

        # 总运行时长
        total_runtime_layout = QVBoxLayout()
        total_runtime_layout.setAlignment(Qt.AlignCenter)
        total_runtime_title = QLabel("总运行时长")
        total_runtime_title.setAlignment(Qt.AlignCenter)
        total_runtime_title.setStyleSheet(f"""
            QLabel {{
                font-size: {self.base_font_size}px;
                color: #7f8c8d;
                font-weight: bold;
            }}
        """)

        self.total_runtime_label = QLabel("0天00:00:00")
        self.total_runtime_label.setAlignment(Qt.AlignCenter)
        runtime_font = QFont()
        runtime_font.setPointSize(int(self.huge_font_size * 0.6))  # 恢复之前的字体大小
        runtime_font.setBold(True)
        self.total_runtime_label.setFont(runtime_font)

        countdown_padding = max(8, int(10 * self.scale_factor))  # 与右边两个保持一致
        countdown_margin = max(4, int(6 * self.scale_factor))    # 与右边两个保持一致
        countdown_radius = max(4, int(6 * self.scale_factor))    # 与右边两个保持一致
        countdown_border = max(1, int(2 * self.scale_factor))

        self.total_runtime_label.setStyleSheet(f"""
            QLabel {{
                color: #27ae60;
                background-color: #f0fff4;
                border: {countdown_border}px solid #c3e6cb;
                border-radius: {countdown_radius}px;
                padding: {countdown_padding}px;
                margin: {countdown_margin}px;
                font-size: {int(self.huge_font_size * 0.6)}px;
                font-weight: bold;
            }}
        """)

        total_runtime_layout.addWidget(total_runtime_title)
        total_runtime_layout.addWidget(self.total_runtime_label)

        # 当前等待时间
        current_wait_layout = QVBoxLayout()
        current_wait_layout.setAlignment(Qt.AlignCenter)
        current_wait_title = QLabel("当前等待时间")
        current_wait_title.setAlignment(Qt.AlignCenter)
        current_wait_title.setStyleSheet(f"""
            QLabel {{
                font-size: {self.base_font_size}px;
                color: #7f8c8d;
                font-weight: bold;
            }}
        """)

        self.countdown_label = QLabel("00:00")
        self.countdown_label.setAlignment(Qt.AlignCenter)
        countdown_font = QFont()
        countdown_font.setPointSize(int(self.huge_font_size * 0.8))
        countdown_font.setBold(True)
        self.countdown_label.setFont(countdown_font)

        self.countdown_label.setStyleSheet(f"""
            QLabel {{
                color: #e74c3c;
                background-color: #fdf2f2;
                border: {countdown_border}px solid #fadbd8;
                border-radius: {countdown_radius}px;
                padding: {countdown_padding}px;
                margin: {countdown_margin}px;
                font-size: {int(self.huge_font_size * 0.8)}px;
                font-weight: bold;
            }}
        """)

        current_wait_layout.addWidget(current_wait_title)
        current_wait_layout.addWidget(self.countdown_label)

        # 平均等待时间
        avg_wait_layout = QVBoxLayout()
        avg_wait_layout.setAlignment(Qt.AlignCenter)
        avg_wait_title = QLabel("平均等待时间")
        avg_wait_title.setAlignment(Qt.AlignCenter)
        avg_wait_title.setStyleSheet(f"""
            QLabel {{
                font-size: {self.base_font_size}px;
                color: #7f8c8d;
                font-weight: bold;
            }}
        """)

        self.avg_wait_label = QLabel("00:00")
        self.avg_wait_label.setAlignment(Qt.AlignCenter)
        self.avg_wait_label.setFont(countdown_font)
        self.avg_wait_label.setStyleSheet(f"""
            QLabel {{
                color: #3498db;
                background-color: #f0f8ff;
                border: {countdown_border}px solid #bde5f8;
                border-radius: {countdown_radius}px;
                padding: {countdown_padding}px;
                margin: {countdown_margin}px;
                font-size: {int(self.huge_font_size * 0.8)}px;
                font-weight: bold;
            }}
        """)

        avg_wait_layout.addWidget(avg_wait_title)
        avg_wait_layout.addWidget(self.avg_wait_label)

        wait_time_layout.addLayout(total_runtime_layout)
        wait_time_layout.addLayout(current_wait_layout)
        wait_time_layout.addLayout(avg_wait_layout)
        countdown_layout.addLayout(wait_time_layout)
        countdown_group.setLayout(countdown_layout)

        # 控制按钮分组
        control_group = QGroupBox("操作控制")
        control_layout = QHBoxLayout()
        control_layout.setSpacing(max(8, int(10 * self.scale_factor)))

        # DPI自适应的按钮尺寸（字体大一号）
        button_height = max(32, int(38 * self.scale_factor))
        button_width = max(80, int(95 * self.scale_factor))
        button_font_size = max(10, int(12 * self.scale_factor))

        self.start_button = QPushButton("🚀 开始复制")
        self.start_button.setMinimumHeight(button_height)
        self.start_button.setMinimumWidth(button_width)
        self.start_button.setStyleSheet(f"""
            QPushButton {{
                background-color: #27ae60;
                font-size: {button_font_size}px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #229954;
            }}
            QPushButton:pressed {{
                background-color: #1e8449;
            }}
        """)
        self.start_button.clicked.connect(self.start_copying)

        self.pause_button = QPushButton("⏸ 暂停复制")
        self.pause_button.setMinimumHeight(button_height)
        self.pause_button.setMinimumWidth(button_width)
        self.pause_button.setStyleSheet(f"""
            QPushButton {{
                background-color: #f39c12;
                font-size: {button_font_size}px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #e67e22;
            }}
            QPushButton:pressed {{
                background-color: #d35400;
            }}
            QPushButton:disabled {{
                background-color: #bdc3c7;
                color: #7f8c8d;
            }}
        """)
        self.pause_button.clicked.connect(self.pause_copying)
        self.pause_button.setEnabled(False)

        self.exit_button = QPushButton("❌ 退出程序")
        self.exit_button.setMinimumHeight(button_height)
        self.exit_button.setMinimumWidth(button_width)
        self.exit_button.setStyleSheet(f"""
            QPushButton {{
                background-color: #e74c3c;
                font-size: {button_font_size}px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #c0392b;
            }}
            QPushButton:pressed {{
                background-color: #a93226;
            }}
        """)
        self.exit_button.clicked.connect(self.exit_program)

        control_layout.addStretch()
        control_layout.addWidget(self.start_button)
        control_layout.addWidget(self.pause_button)
        control_layout.addWidget(self.exit_button)
        control_layout.addStretch()
        control_group.setLayout(control_layout)

        # 添加到主布局
        main_layout.addWidget(path_group)
        main_layout.addWidget(status_group)
        main_layout.addWidget(countdown_group)
        main_layout.addWidget(control_group)
        main_layout.addStretch()

        main_widget.setLayout(main_layout)
        self.setCentralWidget(main_widget)

    def get_program_directory(self):
        """获取程序所在目录（兼容EXE和Python脚本）"""
        if getattr(sys, 'frozen', False):
            # 如果是打包的EXE文件
            return os.path.dirname(sys.executable)
        else:
            # 如果是Python脚本
            return os.path.dirname(os.path.abspath(__file__))

    def load_history_data(self):
        """加载历史数据"""
        try:
            program_dir = self.get_program_directory()
            history_file = os.path.join(program_dir, "Non_WGL.json")

            if os.path.exists(history_file):
                with open(history_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.copied_files_count = data.get('copied_files_count', 0)
                    self.total_runtime = data.get('total_runtime', 0)
                    self.total_copy_time = data.get('total_copy_time', 0)
                    print(f"加载历史数据: 已复制文件数={self.copied_files_count}, 总运行时长={self.total_runtime}秒, 总复制时长={self.total_copy_time}秒")
            else:
                # 文件不存在，创建初始文件
                print(f"历史数据文件不存在，创建新文件: {history_file}")
                self.copied_files_count = 0
                self.total_runtime = 0
                self.total_copy_time = 0
                self.create_initial_history_file()

        except Exception as e:
            print(f"加载历史数据失败: {str(e)}")
            self.copied_files_count = 0
            self.total_runtime = 0
            self.total_copy_time = 0
            # 尝试创建初始文件
            try:
                self.create_initial_history_file()
            except Exception as create_error:
                print(f"创建初始历史文件失败: {str(create_error)}")

    def create_initial_history_file(self):
        """创建初始历史数据文件"""
        try:
            program_dir = self.get_program_directory()
            history_file = os.path.join(program_dir, "Non_WGL.json")

            initial_data = {
                'copied_files_count': 0,
                'total_runtime': 0,
                'total_copy_time': 0,
                'created_time': datetime.now().isoformat(),
                'last_update': datetime.now().isoformat()
            }

            with open(history_file, 'w', encoding='utf-8') as f:
                json.dump(initial_data, f, ensure_ascii=False, indent=2)

            print(f"成功创建初始历史数据文件: {history_file}")

        except Exception as e:
            print(f"创建初始历史数据文件失败: {str(e)}")

    def save_history_data(self):
        """保存历史数据"""
        try:
            program_dir = self.get_program_directory()
            history_file = os.path.join(program_dir, "Non_WGL.json")

            data = {
                'copied_files_count': self.copied_files_count,
                'total_runtime': self.total_runtime,
                'total_copy_time': self.total_copy_time,
                'last_update': datetime.now().isoformat()
            }

            with open(history_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"保存历史数据到: {history_file}")
            print(f"已复制文件数={self.copied_files_count}, 总运行时长={self.total_runtime:.2f}秒, 总复制时长={self.total_copy_time:.2f}秒")
        except Exception as e:
            print(f"保存历史数据失败: {str(e)}")

    def start_monitoring(self):
        """开始监控源文件夹"""
        if os.path.exists(self.source_path):
            self.file_watcher.addPath(self.source_path)
            print(f"开始监控源文件夹: {self.source_path}")

        # 启动定时监控（每5秒检查一次）
        self.file_monitor_timer.start(5000)

        # 启动文件完整性检查定时器（每0.5秒检查一次）
        self.file_check_timer.start(500)

        # 初始扫描
        self.scan_source_folder()

        # 更新状态显示
        self.update_status_display()

    def scan_source_folder(self):
        """扫描源文件夹，将新文件加入队列"""
        if not os.path.exists(self.source_path):
            return

        try:
            # 确保Copied目录存在
            copied_dir = os.path.join(self.source_path, "Copied")
            if not os.path.exists(copied_dir):
                os.makedirs(copied_dir)

            # 获取所有ZIP文件
            for f in os.listdir(self.source_path):
                if f.lower().endswith('.zip') and not f.startswith('~$'):
                    file_path = os.path.join(self.source_path, f)

                    # 检查是否已在队列中
                    if file_path in [item[0] for item in self.file_queue]:
                        continue

                    # 检查是否已在待检查列表中
                    if file_path in self.pending_files:
                        continue

                    # 新文件，加入待检查列表
                    try:
                        file_stat = os.stat(file_path)
                        self.pending_files[file_path] = {
                            'size': file_stat.st_size,
                            'mtime': file_stat.st_mtime,
                            'check_count': 0,
                            'start_time': time.time()
                        }
                        print(f"发现文件，开始完整性检查: {f}")
                    except Exception as e:
                        print(f"获取文件信息失败: {f}, 错误: {str(e)}")

        except Exception as e:
            print(f"扫描源文件夹出错: {str(e)}")

    def on_directory_changed(self, path):
        """文件夹变化事件处理"""
        print(f"检测到文件夹变化: {path}")
        # 延迟5秒后扫描，避免频繁触发
        QTimer.singleShot(5000, self.scan_source_folder)

    def check_file_integrity(self):
        """检查文件完整性 - 每0.5秒检测文件大小和修改时间，连续5次无变化则认为写入完成"""
        completed_files = []

        for file_path, file_info in list(self.pending_files.items()):
            if not os.path.exists(file_path):
                # 文件不存在，移除
                completed_files.append(file_path)
                print(f"文件已不存在，移除检查: {os.path.basename(file_path)}")
                continue

            try:
                # 获取当前文件信息
                file_stat = os.stat(file_path)
                current_size = file_stat.st_size
                current_mtime = file_stat.st_mtime

                # 检查文件大小和修改时间是否有变化
                if (current_size == file_info['size'] and
                    current_mtime == file_info['mtime'] and
                    current_size > 0):
                    # 文件没有变化，增加检查计数
                    file_info['check_count'] += 1

                    if file_info['check_count'] >= 5:
                        # 连续5次检查无变化，认为文件写入完成
                        file_name = os.path.basename(file_path)
                        self.file_queue.append((file_path, file_name, current_size))
                        completed_files.append(file_path)

                        print(f"文件写入完成，加入复制队列: {file_name} ({current_size} bytes)")

                        # 更新状态显示
                        self.update_status_display()

                        # 如果正在运行且没有暂停，且当前没有复制流程在进行，尝试开始复制
                        if (self.is_running and not self.is_paused and
                            not self.folder_check_timer.isActive() and
                            not self.countdown_timer.isActive() and
                            not hasattr(self, 'copy_process_starting')):
                            print("新文件加入队列，启动复制流程")
                            # 设置标志避免重复启动
                            self.copy_process_starting = True
                            # 延迟1秒启动，避免多个文件同时触发
                            QTimer.singleShot(1000, self.delayed_start_copy_process)
                else:
                    # 文件有变化，重置检查计数并更新信息
                    file_info['size'] = current_size
                    file_info['mtime'] = current_mtime
                    file_info['check_count'] = 0

            except Exception as e:
                print(f"检查文件完整性出错: {os.path.basename(file_path)}, 错误: {str(e)}")
                completed_files.append(file_path)

        # 移除已处理的文件
        for file_path in completed_files:
            self.pending_files.pop(file_path, None)

    def monitor_source_folder(self):
        """定时监控源文件夹"""
        self.scan_source_folder()

    def delayed_start_copy_process(self):
        """延迟启动复制流程"""
        # 清除启动标志
        if hasattr(self, 'copy_process_starting'):
            delattr(self, 'copy_process_starting')

        # 启动复制流程
        self.try_start_copy_process()

    def rescan_and_restart(self):
        """重新扫描源文件夹并重启复制流程"""
        if not self.is_running or self.is_paused:
            return

        print("重新扫描源文件夹...")

        # 清理可能卡住的状态
        self.pending_files.clear()

        # 重新扫描源文件夹
        self.scan_source_folder()

        # 如果队列中有文件，启动复制流程
        if len(self.file_queue) > 0:
            print(f"重新扫描后队列中有 {len(self.file_queue)} 个文件，启动复制流程")
            self.try_start_copy_process()
        else:
            print("重新扫描后队列仍为空，继续监控")

    def try_start_copy_process(self):
        """尝试开始复制流程"""
        if not self.is_running or self.is_paused or len(self.file_queue) == 0:
            print("无法启动复制流程：程序未运行、已暂停或队列为空")
            return

        # 如果当前没有在进行复制流程，开始新的复制流程
        if not self.folder_check_timer.isActive() and not self.countdown_timer.isActive():
            print(f"开始新的复制流程，队列中有 {len(self.file_queue)} 个文件")
            self.current_file_start_time = time.time()  # 记录开始时间

            # 开始复制任务计时
            if not self.is_copy_task_active:
                self.is_copy_task_active = True
                self.copy_task_start_time = time.time()
                print("开始复制任务计时")

            self.waiting_seconds = 0
            self.countdown_label.setText("00:00")
            self.countdown_timer.start(1000)
            self.folder_check_timer.start(5000)  # 每5秒检查一次目标文件夹
            self.check_target_folder()  # 立即检查一次
        else:
            print("复制流程已在进行中，等待当前流程完成")

    def can_copy_two_files(self):
        """检查是否可以同时复制两个文件（都小于10MB）"""
        if len(self.file_queue) < 2:
            return False

        file1_size = self.file_queue[0][2]  # 第一个文件大小
        file2_size = self.file_queue[1][2]  # 第二个文件大小

        # 10MB = 10 * 1024 * 1024 bytes
        max_size = 10 * 1024 * 1024

        return file1_size < max_size and file2_size < max_size

    def select_source_folder(self):
        folder = QFileDialog.getExistingDirectory(self, "选择源文件夹", self.source_path)
        if folder:
            # 停止当前监控
            self.file_watcher.removePath(self.source_path)

            self.source_path = folder
            self.source_edit.setText(folder)

            # 重新开始监控
            self.file_watcher.addPath(self.source_path)
            self.scan_source_folder()
            self.update_status_display()

    def select_target_folder(self):
        folder = QFileDialog.getExistingDirectory(self, "选择目标文件夹", self.target_path)
        if folder:
            self.target_path = folder
            self.target_edit.setText(folder)

    def format_time_duration(self, seconds):
        """格式化时间长度为x天hh:mm:ss格式"""
        if seconds < 0:
            return "0天00:00:00"

        days = int(seconds // 86400)  # 86400秒 = 1天
        hours = int((seconds % 86400) // 3600)  # 3600秒 = 1小时
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)

        return f"{days}天{hours:02d}:{minutes:02d}:{secs:02d}"

    def format_time_hhmm(self, seconds):
        """格式化时间为HH:MM格式"""
        if seconds < 0:
            return "--:--"
        minutes = int(seconds // 60)
        secs = int(seconds % 60)
        return f"{minutes:02d}:{secs:02d}"

    def update_runtime_display(self):
        """更新总运行时长显示"""
        if self.is_running and self.start_time:
            current_runtime = self.total_runtime + (time.time() - self.start_time.timestamp())
        else:
            current_runtime = self.total_runtime

        self.total_runtime_label.setText(self.format_time_duration(current_runtime))

        # 同时更新复制任务时间（如果有活跃的复制任务）
        if self.is_copy_task_active and self.copy_task_start_time:
            current_copy_time = self.total_copy_time + (time.time() - self.copy_task_start_time)
            # 实时更新平均等待时间
            if self.copied_files_count > 0:
                self.average_wait_time = current_copy_time / self.copied_files_count

    def get_remaining_files_count(self):
        """获取E:\ZIP目录下剩余待复制文件数量"""
        if not os.path.exists(self.source_path):
            return 0

        try:
            remaining_count = 0
            for f in os.listdir(self.source_path):
                if f.lower().endswith('.zip') and not f.startswith('~$'):
                    remaining_count += 1
            return remaining_count
        except Exception as e:
            print(f"获取剩余文件数出错: {str(e)}")
            return 0

    def update_status_display(self):
        """更新状态显示"""
        queue_count = len(self.file_queue)
        remaining_files_count = self.get_remaining_files_count()

        # 更新文件统计
        self.file_stats_label.setText(
            f"已复制文件数: {self.copied_files_count} | 剩余文件数: {remaining_files_count}"
        )

        # 更新时间统计（使用总复制文件时长计算平均等待时间）
        if self.copied_files_count > 0:
            # 如果有活跃的复制任务，使用实时的复制时间
            if self.is_copy_task_active and self.copy_task_start_time:
                current_copy_time = self.total_copy_time + (time.time() - self.copy_task_start_time)
            else:
                current_copy_time = self.total_copy_time

            self.average_wait_time = current_copy_time / self.copied_files_count
            avg_time_str = self.format_time_hhmm(self.average_wait_time)
        else:
            avg_time_str = "00:00"

        # 计算预计剩余时间
        if queue_count > 0 and self.average_wait_time > 0:
            estimated_remaining_seconds = queue_count * self.average_wait_time
            est_time_str = self.format_time_hhmm(estimated_remaining_seconds)
        else:
            est_time_str = "--:--"

        # 最后操作时间（北京时间）
        if hasattr(self, 'last_operation_time'):
            last_time_str = self.last_operation_time.strftime('%H:%M:%S')
        else:
            last_time_str = "--:--:--"

        self.time_stats_label.setText(
            f"预计剩余时间: {est_time_str} | 最后操作时间: {last_time_str}"
        )

        # 更新平均等待时间显示
        self.avg_wait_label.setText(avg_time_str)

    def update_countdown(self):
        """更新等待时间显示"""
        if self.is_running and not self.is_paused:
            self.waiting_seconds += 1
            # 格式化为 MM:SS
            minutes = self.waiting_seconds // 60
            seconds = self.waiting_seconds % 60
            self.countdown_label.setText(f"{minutes:02d}:{seconds:02d}")

    def check_target_folder(self):
        """检查目标文件夹是否为空"""
        if not self.is_running or self.is_paused or len(self.file_queue) == 0:
            print("停止目标文件夹检查：程序未运行、已暂停或队列为空")
            self.folder_check_timer.stop()
            self.countdown_timer.stop()
            return

        try:
            # 检查目标文件夹是否为空
            if not os.path.exists(self.target_path):
                os.makedirs(self.target_path)
                print(f"创建目标目录: {self.target_path}")

            target_files = [f for f in os.listdir(self.target_path)
                            if f.lower().endswith('.zip')]

            print(f"目标文件夹检查：发现 {len(target_files)} 个ZIP文件")

            if not target_files:  # 如果目标文件夹为空
                # 停止检查定时器和倒计时
                self.folder_check_timer.stop()
                self.countdown_timer.stop()

                # 5秒后执行复制
                print("目标文件夹为空，5秒后开始复制")
                QTimer.singleShot(5000, self.copy_next_files)
            else:
                print(f"目标文件夹不为空，继续等待。文件: {target_files}")
            # 否则不做任何操作，继续等待下次检查
        except Exception as e:
            print(f"检查目标文件夹出错: {str(e)}")

    def start_copying(self):
        # 确保目标路径存在
        try:
            if not os.path.exists(self.target_path):
                os.makedirs(self.target_path)
                print(f"目标路径不存在，已自动创建: {self.target_path}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法创建目标路径: {str(e)}")
            return

        # 重置状态
        self.is_running = True
        self.is_paused = False
        self.start_time = datetime.now()

        # 更新按钮状态
        self.start_button.setEnabled(False)
        self.pause_button.setEnabled(True)
        self.pause_button.setStyleSheet(f"""
            QPushButton {{
                background-color: #f39c12;
                font-size: {max(10, int(12 * self.scale_factor))}px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #e67e22;
            }}
            QPushButton:pressed {{
                background-color: #d35400;
            }}
        """)

        # 隐藏完成信息标签
        self.completion_label.setVisible(False)

        print("开始自动复制模式")

        # 重新检测源文件夹和目标文件夹状态
        print("重新检测文件状态...")

        # 清理可能的残留状态
        self.pending_files.clear()
        if hasattr(self, 'copy_process_starting'):
            delattr(self, 'copy_process_starting')

        # 重新扫描源文件夹
        self.scan_source_folder()

        # 如果队列中有文件，尝试开始复制流程
        if len(self.file_queue) > 0:
            print(f"检测到队列中有 {len(self.file_queue)} 个文件，启动复制流程")
            self.try_start_copy_process()
        else:
            print("队列为空，继续监控源文件夹")

        # 更新状态显示
        self.update_status_display()

    def copy_next_files(self):
        """复制下一个或下两个文件"""
        if len(self.file_queue) == 0 or not self.is_running or self.is_paused:
            print("无法复制：队列为空或程序未运行或已暂停")
            return

        copy_start_time = time.time()
        copied_dir = os.path.join(self.source_path, "Copied")
        files_to_copy = []

        try:
            # 确保目标目录存在
            if not os.path.exists(self.target_path):
                os.makedirs(self.target_path)
                print(f"创建目标目录: {self.target_path}")

            # 确保Copied目录存在
            if not os.path.exists(copied_dir):
                os.makedirs(copied_dir)
                print(f"创建Copied目录: {copied_dir}")

            # 决定复制一个还是两个文件
            if self.can_copy_two_files():
                # 复制两个文件
                files_to_copy = [self.file_queue.popleft(), self.file_queue.popleft()]
                print("同时复制两个小文件")
            else:
                # 复制一个文件
                files_to_copy = [self.file_queue.popleft()]
                print("复制一个文件")

            # 执行复制操作
            for file_path, file_name, file_size in files_to_copy:
                if not os.path.exists(file_path):
                    print(f"源文件不存在，跳过: {file_name}")
                    continue

                target_file = os.path.join(self.target_path, file_name)

                # 复制文件到目标路径
                print(f"开始复制: {file_name} -> {self.target_path}")
                shutil.copy2(file_path, target_file)
                print(f"复制完成: {file_name}")

                # 移动源文件到Copied目录
                copied_file_path = os.path.join(copied_dir, file_name)
                shutil.move(file_path, copied_file_path)
                print(f"移动到Copied目录: {file_name}")

                # 更新统计
                self.copied_files_count += 1

                # 立即更新平均等待时间
                if self.copied_files_count > 0:
                    self.average_wait_time = self.total_copy_time / self.copied_files_count

                # 实时更新状态显示
                self.update_status_display()

            # 计算本次复制时间
            copy_end_time = time.time()
            copy_duration = copy_end_time - copy_start_time
            self.last_copy_time = copy_duration

            # 记录最后操作时间（北京时间）
            self.last_operation_time = datetime.now()

            # 更新总运行时间（包括等待时间）
            if hasattr(self, 'current_file_start_time') and self.current_file_start_time:
                total_duration = copy_end_time - self.current_file_start_time
                self.total_runtime += total_duration

            print(f"复制耗时: {copy_duration:.2f}秒，总耗时: {total_duration:.2f}秒")

            # 更新复制任务时间（每次复制完文件后立即更新）
            if self.is_copy_task_active and self.copy_task_start_time:
                # 计算当前复制任务已用时间并累加到总复制时间
                current_task_time = time.time() - self.copy_task_start_time
                self.total_copy_time += current_task_time
                # 重置复制任务开始时间，继续计时
                self.copy_task_start_time = time.time()
                print(f"更新复制任务时间，累计复制时长: {self.total_copy_time:.2f}秒")

            # 保存历史数据
            self.save_history_data()

            # 更新状态显示
            self.update_status_display()

            # 检查是否还有文件需要复制（包括队列中的和源文件夹中新的）
            remaining_files_count = self.get_remaining_files_count()

            if len(self.file_queue) > 0 and self.is_running and not self.is_paused:
                print(f"队列中还有 {len(self.file_queue)} 个文件，等待5秒后开始下一轮复制流程")
                # 等待5秒后开始下一轮复制流程，确保每次复制前都检查目标文件夹
                QTimer.singleShot(5000, self.try_start_copy_process)
            elif remaining_files_count > 0 and self.is_running and not self.is_paused:
                print(f"队列为空但源文件夹还有 {remaining_files_count} 个文件，重新扫描源文件夹")
                # 停止当前复制流程
                self.folder_check_timer.stop()
                self.countdown_timer.stop()
                # 重新扫描源文件夹，可能有文件没有被检测到
                QTimer.singleShot(2000, self.rescan_and_restart)
            else:
                # 停止目标文件夹检查，但继续监控源文件夹
                self.folder_check_timer.stop()
                self.countdown_timer.stop()

                # 停止复制任务计时
                if self.is_copy_task_active:
                    self.is_copy_task_active = False
                    if self.copy_task_start_time:
                        # 最后一次更新复制任务时间
                        final_task_time = time.time() - self.copy_task_start_time
                        self.total_copy_time += final_task_time
                        print(f"复制任务完全结束，最终累计复制时长: {self.total_copy_time:.2f}秒")
                        self.copy_task_start_time = None

                if remaining_files_count == 0:
                    print("所有文件已复制完成，停止目标文件夹检查，继续监控源文件夹")
                else:
                    print("程序已停止或暂停，停止复制流程")

        except Exception as e:
            print(f"复制文件时出错: {str(e)}")
            import traceback
            traceback.print_exc()

            # 出错时将文件重新加入队列
            for file_info in reversed(files_to_copy):
                self.file_queue.appendleft(file_info)
            print("文件已重新加入队列")

    def pause_copying(self):
        """暂停复制"""
        if not self.is_running:
            return

        # 暂停复制
        self.is_paused = True
        self.countdown_timer.stop()
        self.folder_check_timer.stop()

        # 暂停时更新复制任务时间
        if self.is_copy_task_active and self.copy_task_start_time:
            pause_task_time = time.time() - self.copy_task_start_time
            self.total_copy_time += pause_task_time
            print(f"暂停时更新复制时间，累计复制时长: {self.total_copy_time:.2f}秒")
            self.copy_task_start_time = None  # 清除开始时间

        # 更新按钮状态
        self.pause_button.setEnabled(False)
        self.pause_button.setStyleSheet(f"""
            QPushButton {{
                background-color: #bdc3c7;
                color: #7f8c8d;
                font-size: {max(10, int(12 * self.scale_factor))}px;
                font-weight: bold;
            }}
        """)

        self.start_button.setEnabled(True)

        # 保存数据
        self.save_history_data()

        print("复制已暂停")

    def exit_program(self):
        """退出程序"""
        # 退出前更新复制任务时间
        if self.is_copy_task_active and self.copy_task_start_time:
            exit_task_time = time.time() - self.copy_task_start_time
            self.total_copy_time += exit_task_time
            print(f"退出前更新复制时间，最终累计复制时长: {self.total_copy_time:.2f}秒")

        # 保存历史数据
        self.save_history_data()

        # 停止所有定时器
        self.countdown_timer.stop()
        self.folder_check_timer.stop()
        self.file_monitor_timer.stop()
        self.file_check_timer.stop()
        self.runtime_update_timer.stop()

        # 关闭文件监控
        self.file_watcher.removePath(self.source_path)

        print("程序退出")
        self.close()

    def closeEvent(self, event):
        """程序关闭事件"""
        # 关闭前更新复制任务时间
        if self.is_copy_task_active and self.copy_task_start_time:
            close_task_time = time.time() - self.copy_task_start_time
            self.total_copy_time += close_task_time
            print(f"关闭前更新复制时间，最终累计复制时长: {self.total_copy_time:.2f}秒")

        self.save_history_data()
        event.accept()


def setup_high_dpi_support():
    """设置高DPI支持和字体渲染优化"""
    # 启用高DPI支持
    if hasattr(Qt, 'AA_EnableHighDpiScaling'):
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)

    if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

    # 设置DPI缩放策略
    if hasattr(QApplication, 'setHighDpiScaleFactorRoundingPolicy'):
        QApplication.setHighDpiScaleFactorRoundingPolicy(Qt.HighDpiScaleFactorRoundingPolicy.PassThrough)

    # 启用字体抗锯齿和渲染优化
    if hasattr(Qt, 'AA_UseDesktopOpenGL'):
        QApplication.setAttribute(Qt.AA_UseDesktopOpenGL, True)

    # 启用字体子像素渲染
    if hasattr(Qt, 'AA_UseSoftwareOpenGL'):
        QApplication.setAttribute(Qt.AA_UseSoftwareOpenGL, False)


if __name__ == "__main__":
    # 在创建QApplication之前设置高DPI支持
    setup_high_dpi_support()

    app = QApplication(sys.argv)

    # 设置默认字体（确保中文显示正常）
    font_db = QFontDatabase()

    # 尝试设置系统默认字体
    default_font = QFont()
    default_font.setFamily("Microsoft YaHei UI")  # 微软雅黑
    if not font_db.families().__contains__("Microsoft YaHei UI"):
        default_font.setFamily("SimHei")  # 黑体
    if not font_db.families().__contains__("SimHei"):
        default_font.setFamily("Arial Unicode MS")  # 备用字体

    default_font.setHintingPreference(QFont.PreferFullHinting)
    default_font.setStyleStrategy(QFont.PreferAntialias)
    app.setFont(default_font)

    # 创建并显示窗口
    window = FileCopyApp()
    window.show()

    # 居中显示窗口
    desktop = QDesktopWidget()
    screen_rect = desktop.screenGeometry()
    window_rect = window.geometry()
    x = (screen_rect.width() - window_rect.width()) // 2
    y = (screen_rect.height() - window_rect.height()) // 2
    window.move(x, y)

    sys.exit(app.exec_())
