import os
import shutil
import sys
import json
import logging
import time
import zipfile
import threading
from datetime import datetime
from tkinter import *
from tkinter import ttk, messagebox, filedialog

# 配置日志记录
log_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'enc_auto_xfer.log')
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8', mode='a'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 定义常量
FOLDER_MONITOR_INTERVAL = 5000  # 文件夹监控间隔(ms)
FOLDER_WRITE_CHECK_DELAY = 5000  # 文件夹写入完成检测延时(ms)

class ENCAutoXferApp:
    def __init__(self, root):
        self.root = root
        self.root.title("ENC WQAR数据自动传输程序")
        self.root.geometry("700x600")
        
        # 初始化路径属性
        self.path1 = r'Z:\DATA_BAK\ENC_BAK'  # 源文件夹
        self.path2 = r'E:\ENC'  # 目标文件夹
        self.cast_source = os.path.join(self.path2, 'ENC_CAST')
        self.comac_source = os.path.join(self.path2, 'ENC_COMAC')
        self.cast_target = r'D:\NON-WGL\CAST'
        self.comac_target = r'D:\NON-WGL\COMAC'
        
        # 初始化配置文件路径
        self.config_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "ENC.json")
        self.ac_type_file = r'Z:\DATA_BAK\config\AC_TYPE.json'
        
        # 初始化统计属性
        self.enc_file_count = 0
        self.cast_copied_files = 0
        self.comac_copied_files = 0
        self.total_runtime_seconds = 0
        self.cast_total_time = 0
        self.comac_total_time = 0
        
        # 初始化状态属性
        self.is_running = False
        self.is_cast_copying = False
        self.is_comac_copying = False
        self.start_time = None
        
        # 初始化选择框状态
        self.cast_enabled = BooleanVar(value=True)
        self.comac_enabled = BooleanVar(value=True)
        
        # 初始化监控变量
        self.processed_folders = set()
        self.pending_folders = []
        self.processing_folders = []
        self.last_folder_sizes = {}
        self.cast_pending_files = []
        self.comac_pending_files = []
        self.cast_waiting_seconds = 0
        self.comac_waiting_seconds = 0
        
        # 加载配置
        self.load_config()
        self.load_ac_type_config()
        
        # 创建UI
        self.create_ui()
        
        # 启动监控线程
        self.monitor_thread = None
        self.stop_monitoring = False

    def load_ac_type_config(self):
        """加载飞机型号配置文件"""
        try:
            # 首先尝试加载主配置文件
            if os.path.exists(self.ac_type_file):
                # 尝试多种编码方式
                encodings = ['utf-8-sig', 'utf-8', 'gbk', 'latin1']
                for encoding in encodings:
                    try:
                        with open(self.ac_type_file, 'r', encoding=encoding) as f:
                            self.ac_type_map = json.load(f)
                        logger.info(f"成功加载主配置文件（编码：{encoding}），共{len(self.ac_type_map)}架飞机")
                        return
                    except (UnicodeDecodeError, json.JSONDecodeError):
                        continue
                raise Exception("所有编码方式都失败")
            else:
                # 如果主配置文件不存在，尝试使用测试配置文件
                test_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "test_AC_TYPE.json")
                if os.path.exists(test_file):
                    with open(test_file, 'r', encoding='utf-8') as f:
                        self.ac_type_map = json.load(f)
                    logger.info(f"使用测试配置文件，共{len(self.ac_type_map)}架飞机")
                else:
                    self.ac_type_map = {}
                    logger.warning(f"配置文件不存在: {self.ac_type_file} 和 {test_file}")
        except Exception as e:
            self.ac_type_map = {}
            logger.error(f"加载飞机型号配置失败: {str(e)}")

    def is_arj21_aircraft(self, aircraft_code):
        """判断是否为ARJ21机型"""
        return self.ac_type_map.get(aircraft_code, '') == 'ARJ21'

    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.enc_file_count = config.get('enc_file_count', 0)
                    self.cast_copied_files = config.get('cast_copied_files', 0)
                    self.comac_copied_files = config.get('comac_copied_files', 0)
                    self.total_runtime_seconds = config.get('total_runtime_seconds', 0)
                    self.cast_total_time = config.get('cast_total_time', 0)
                    self.comac_total_time = config.get('comac_total_time', 0)
        except Exception as e:
            logger.error(f"加载配置文件出错: {str(e)}")

    def save_config(self):
        """保存配置文件"""
        try:
            config = {
                'enc_file_count': self.enc_file_count,
                'cast_copied_files': self.cast_copied_files,
                'comac_copied_files': self.comac_copied_files,
                'total_runtime_seconds': self.total_runtime_seconds,
                'cast_total_time': self.cast_total_time,
                'comac_total_time': self.comac_total_time,
                'cast_enabled': self.cast_enabled.get(),
                'comac_enabled': self.comac_enabled.get(),
                'last_run_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
        except Exception as e:
            logger.error(f"保存配置文件出错: {str(e)}")

    def create_ui(self):
        """创建用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(W, E, N, S))
        
        # 路径设置框架
        path_frame = ttk.LabelFrame(main_frame, text="路径设置", padding="10")
        path_frame.grid(row=0, column=0, columnspan=2, sticky=(W, E), pady=(0, 10))
        
        ttk.Label(path_frame, text="源文件夹:").grid(row=0, column=0, sticky=W, padx=(0, 10))
        self.source_var = StringVar(value=self.path1)
        ttk.Entry(path_frame, textvariable=self.source_var, state='readonly', width=60).grid(row=0, column=1, sticky=(W, E))
        
        ttk.Label(path_frame, text="目标文件夹:").grid(row=1, column=0, sticky=W, padx=(0, 10), pady=(5, 0))
        self.target_var = StringVar(value=self.path2)
        ttk.Entry(path_frame, textvariable=self.target_var, state='readonly', width=60).grid(row=1, column=1, sticky=(W, E), pady=(5, 0))
        
        # 复制选项框架
        option_frame = ttk.LabelFrame(main_frame, text="复制选项", padding="10")
        option_frame.grid(row=1, column=0, columnspan=2, sticky=(W, E), pady=(0, 10))
        
        ttk.Checkbutton(option_frame, text="CAST数据复制", variable=self.cast_enabled).grid(row=0, column=0, sticky=W, padx=(0, 20))
        ttk.Checkbutton(option_frame, text="COMAC数据复制", variable=self.comac_enabled).grid(row=0, column=1, sticky=W)
        
        # 状态信息框架
        status_frame = ttk.LabelFrame(main_frame, text="复制状态", padding="10")
        status_frame.grid(row=2, column=0, columnspan=2, sticky=(W, E), pady=(0, 10))
        
        self.cast_status_var = StringVar(value="CAST: 等待开始...")
        ttk.Label(status_frame, textvariable=self.cast_status_var, font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky=W, pady=(0, 5))
        
        self.comac_status_var = StringVar(value="COMAC: 等待开始...")
        ttk.Label(status_frame, textvariable=self.comac_status_var, font=('Arial', 10, 'bold')).grid(row=1, column=0, sticky=W, pady=(0, 5))
        
        self.stats_var = StringVar(value=f"ENC文件数: {self.enc_file_count} | CAST已复制: {self.cast_copied_files} | COMAC已复制: {self.comac_copied_files}")
        ttk.Label(status_frame, textvariable=self.stats_var).grid(row=2, column=0, sticky=W)
        
        # 等待时间框架
        time_frame = ttk.LabelFrame(main_frame, text="等待时间", padding="10")
        time_frame.grid(row=3, column=0, columnspan=2, sticky=(W, E), pady=(0, 10))
        
        self.current_wait_var = StringVar(value="当前等待: 00:00")
        ttk.Label(time_frame, textvariable=self.current_wait_var, font=('Arial', 12, 'bold')).grid(row=0, column=0, padx=(0, 20))
        
        self.avg_wait_var = StringVar(value="平均等待: 00:00")
        ttk.Label(time_frame, textvariable=self.avg_wait_var, font=('Arial', 12, 'bold')).grid(row=0, column=1)
        
        # 控制按钮框架
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=4, column=0, columnspan=2, pady=(10, 0))
        
        self.start_button = ttk.Button(control_frame, text="🚀 开始监控", command=self.start_monitoring)
        self.start_button.grid(row=0, column=0, padx=(0, 10))
        
        self.stop_button = ttk.Button(control_frame, text="⏹ 暂停监控", command=self.stop_monitoring_func, state='disabled')
        self.stop_button.grid(row=0, column=1)
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        path_frame.columnconfigure(1, weight=1)

    def update_stats_display(self):
        """更新统计信息显示"""
        self.stats_var.set(
            f"ENC文件数: {self.enc_file_count} | CAST已复制: {self.cast_copied_files} | COMAC已复制: {self.comac_copied_files}"
        )

    def get_folder_size(self, folder_path):
        """计算文件夹大小"""
        total_size = 0
        try:
            for dirpath, dirnames, filenames in os.walk(folder_path):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    if os.path.exists(filepath):
                        total_size += os.path.getsize(filepath)
        except Exception as e:
            logger.error(f"计算文件夹大小出错: {str(e)}")
        return total_size

    def create_zip_file(self, source_folder, zip_path):
        """创建ZIP文件，只包含文件夹内的文件，不包含文件夹本身"""
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(source_folder):
                for file in files:
                    file_path = os.path.join(root, file)
                    # 计算相对路径，不包含源文件夹名
                    arcname = os.path.relpath(file_path, source_folder)
                    zipf.write(file_path, arcname)

    def process_folder(self, folder_name):
        """处理新文件夹：压缩并分类"""
        try:
            # 提取飞机号（前6个字符）
            aircraft_code = folder_name[:6].upper()
            logger.info(f"处理文件夹: {folder_name}, 飞机号: {aircraft_code}")

            # 创建目标目录
            os.makedirs(self.cast_source, exist_ok=True)
            os.makedirs(self.comac_source, exist_ok=True)

            # 压缩文件夹
            zip_filename = f"{folder_name}M.zip"
            source_folder_path = os.path.join(self.path1, folder_name)
            cast_zip_path = os.path.join(self.cast_source, zip_filename)

            self.create_zip_file(source_folder_path, cast_zip_path)
            logger.info(f"创建压缩文件: {cast_zip_path}")

            # 判断是否为ARJ21机型
            if self.is_arj21_aircraft(aircraft_code):
                comac_zip_path = os.path.join(self.comac_source, zip_filename)
                shutil.copy2(cast_zip_path, comac_zip_path)
                logger.info(f"ARJ21机型，复制到COMAC: {comac_zip_path}")

            # 更新统计
            self.enc_file_count += 1
            self.processed_folders.add(folder_name)
            self.root.after(0, self.update_stats_display)
            self.save_config()

            logger.info(f"文件夹 {folder_name} 处理完成")

        except Exception as e:
            logger.error(f"处理文件夹 {folder_name} 出错: {str(e)}")

    def start_monitoring(self):
        """开始监控"""
        # 确保必要的目录存在
        try:
            os.makedirs(self.path1, exist_ok=True)
            os.makedirs(self.path2, exist_ok=True)
            os.makedirs(self.cast_source, exist_ok=True)
            os.makedirs(self.comac_source, exist_ok=True)
            os.makedirs(self.cast_target, exist_ok=True)
            os.makedirs(self.comac_target, exist_ok=True)
        except Exception as e:
            messagebox.showerror("错误", f"无法创建必要目录: {str(e)}")
            return

        self.is_running = True
        self.start_time = datetime.now()
        self.start_button.config(state='disabled')
        self.stop_button.config(state='normal')
        self.stop_monitoring = False

        self.cast_status_var.set("CAST: 监控中...")
        self.comac_status_var.set("COMAC: 监控中...")
        
        # 启动监控线程
        self.monitor_thread = threading.Thread(target=self.monitoring_loop, daemon=True)
        self.monitor_thread.start()
        
        logger.info("开始监控")

    def stop_monitoring_func(self):
        """停止监控"""
        self.is_running = False
        self.stop_monitoring = True
        
        self.start_button.config(state='normal')
        self.stop_button.config(state='disabled')
        
        self.cast_status_var.set("CAST: 已停止")
        self.comac_status_var.set("COMAC: 已停止")
        
        # 保存配置
        self.save_config()
        
        logger.info("监控已停止")

    def monitoring_loop(self):
        """监控循环"""
        while self.is_running and not self.stop_monitoring:
            try:
                # 监控源文件夹
                self.monitor_source_folder()
                
                # 检查目标文件夹
                if self.cast_enabled.get():
                    self.check_cast_folder_empty()
                
                if self.comac_enabled.get():
                    self.check_comac_folder_empty()
                
                time.sleep(5)  # 每5秒检查一次
                
            except Exception as e:
                logger.error(f"监控循环出错: {str(e)}")
                time.sleep(5)

    def monitor_source_folder(self):
        """监控源文件夹，检测新文件夹"""
        if not self.is_running:
            return

        try:
            # 获取所有文件夹
            if not os.path.exists(self.path1):
                return
                
            current_folders = [f for f in os.listdir(self.path1)
                             if os.path.isdir(os.path.join(self.path1, f)) and not f.startswith('.')]

            # 检查新文件夹
            for folder in current_folders:
                if folder not in self.processed_folders and folder not in self.processing_folders:
                    self.processing_folders.append(folder)
                    self.check_folder_write_complete(folder)

        except Exception as e:
            logger.error(f"监控源文件夹出错: {str(e)}")

    def check_folder_write_complete(self, folder):
        """检查文件夹是否写入完成"""
        if not self.is_running:
            return

        folder_path = os.path.join(self.path1, folder)
        try:
            if not os.path.exists(folder_path):
                if folder in self.processing_folders:
                    self.processing_folders.remove(folder)
                return

            # 计算文件夹大小
            current_size = self.get_folder_size(folder_path)

            if folder in self.last_folder_sizes:
                # 如果文件夹大小没有变化，认为写入完成
                if current_size == self.last_folder_sizes[folder]:
                    if folder in self.processing_folders:
                        self.processing_folders.remove(folder)
                    self.process_folder(folder)
                    return
                else:
                    self.last_folder_sizes[folder] = current_size
            else:
                self.last_folder_sizes[folder] = current_size

            # 5秒后再次检查
            time.sleep(5)
            if self.is_running:
                self.check_folder_write_complete(folder)
                
        except Exception as e:
            logger.error(f"检查文件夹写入完成出错: {str(e)}")
            if folder in self.processing_folders:
                self.processing_folders.remove(folder)

    def check_cast_folder_empty(self):
        """检查CAST目标文件夹是否为空"""
        if not self.is_running or not self.cast_enabled.get() or self.is_cast_copying:
            return

        try:
            if not os.path.exists(self.cast_target):
                return
                
            target_files = [f for f in os.listdir(self.cast_target) if not f.startswith('.')]
            
            if len(target_files) == 0:  # 文件夹为空
                # 检查是否有待复制文件
                if os.path.exists(self.cast_source):
                    source_files = [f for f in os.listdir(self.cast_source) if f.lower().endswith('.zip')]
                    
                    if source_files and not self.is_cast_copying:
                        self.is_cast_copying = True
                        self.copy_cast_files(source_files[0])
                        
        except Exception as e:
            logger.error(f"检查CAST文件夹出错: {str(e)}")

    def check_comac_folder_empty(self):
        """检查COMAC目标文件夹是否为空"""
        if not self.is_running or not self.comac_enabled.get() or self.is_comac_copying:
            return

        try:
            if not os.path.exists(self.comac_target):
                return
                
            target_files = [f for f in os.listdir(self.comac_target) if not f.startswith('.')]
            
            if len(target_files) == 0:  # 文件夹为空
                # 检查是否有待复制文件
                if os.path.exists(self.comac_source):
                    source_files = [f for f in os.listdir(self.comac_source) if f.lower().endswith('.zip')]
                    
                    if source_files and not self.is_comac_copying:
                        self.is_comac_copying = True
                        self.copy_comac_files(source_files[0])
                        
        except Exception as e:
            logger.error(f"检查COMAC文件夹出错: {str(e)}")

    def copy_cast_files(self, file_to_copy):
        """复制CAST文件"""
        try:
            source_path = os.path.join(self.cast_source, file_to_copy)
            target_path = os.path.join(self.cast_target, file_to_copy)
            
            self.root.after(0, lambda: self.cast_status_var.set(f"CAST: 正在复制 {file_to_copy}"))
            
            copy_start_time = time.time()
            
            # 确保目标目录存在
            os.makedirs(self.cast_target, exist_ok=True)
            
            # 复制文件
            shutil.copy2(source_path, target_path)
            
            # 移动源文件到已复制目录
            copied_dir = os.path.join(self.cast_source, "Copied")
            os.makedirs(copied_dir, exist_ok=True)
            shutil.move(source_path, os.path.join(copied_dir, file_to_copy))
            
            # 更新统计
            copy_duration = time.time() - copy_start_time
            self.cast_total_time += copy_duration
            self.cast_copied_files += 1
            
            self.root.after(0, lambda: self.cast_status_var.set(f"CAST: 复制完成 {file_to_copy}"))
            self.root.after(0, self.update_stats_display)
            self.save_config()
            
            logger.info(f"CAST文件复制完成: {file_to_copy}")
            
        except Exception as e:
            self.root.after(0, lambda: self.cast_status_var.set(f"CAST: 复制失败 {str(e)}"))
            logger.error(f"CAST文件复制失败: {str(e)}")
        
        finally:
            self.is_cast_copying = False

    def copy_comac_files(self, file_to_copy):
        """复制COMAC文件"""
        try:
            source_path = os.path.join(self.comac_source, file_to_copy)
            target_path = os.path.join(self.comac_target, file_to_copy)
            
            self.root.after(0, lambda: self.comac_status_var.set(f"COMAC: 正在复制 {file_to_copy}"))
            
            copy_start_time = time.time()
            
            # 确保目标目录存在
            os.makedirs(self.comac_target, exist_ok=True)
            
            # 复制文件
            shutil.copy2(source_path, target_path)
            
            # 移动源文件到已复制目录
            copied_dir = os.path.join(self.comac_source, "Copied")
            os.makedirs(copied_dir, exist_ok=True)
            shutil.move(source_path, os.path.join(copied_dir, file_to_copy))
            
            # 更新统计
            copy_duration = time.time() - copy_start_time
            self.comac_total_time += copy_duration
            self.comac_copied_files += 1
            
            self.root.after(0, lambda: self.comac_status_var.set(f"COMAC: 复制完成 {file_to_copy}"))
            self.root.after(0, self.update_stats_display)
            self.save_config()
            
            logger.info(f"COMAC文件复制完成: {file_to_copy}")
            
        except Exception as e:
            self.root.after(0, lambda: self.comac_status_var.set(f"COMAC: 复制失败 {str(e)}"))
            logger.error(f"COMAC文件复制失败: {str(e)}")
        
        finally:
            self.is_comac_copying = False

if __name__ == "__main__":
    root = Tk()
    app = ENCAutoXferApp(root)
    
    # 程序关闭时保存配置
    def on_closing():
        app.stop_monitoring_func()
        app.save_config()
        root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()
